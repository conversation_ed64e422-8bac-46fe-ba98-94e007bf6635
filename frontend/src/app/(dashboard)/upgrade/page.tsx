/**
 * Plan Upgrade Page
 * 
 * Dedicated page for upgrading subscription plans with comparison
 * and seamless payment integration.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Check, 
  ArrowRight, 
  TrendingUp, 
  Users, 
  Zap, 
  Shield,
  Star,
  Clock,
  CreditCard
} from 'lucide-react';
import { PaymentMethodManager } from '@/components/payment/PaymentMethodManager';
import type { StripePaymentMethodCreationResponse } from '@/lib/types/payment-methods';

interface Plan {
  id: string;
  name: string;
  price: number;
  currency: string;
  popular?: boolean;
  features: string[];
  limits: {
    users: number;
    cases: number;
    storage: number;
    aiRequests: number;
  };
}

const PLANS: Plan[] = [
  {
    id: 'solo',
    name: 'Solo Practitioner',
    price: 99,
    currency: 'USD',
    features: [
      'Personal Injury & Medical Malpractice',
      'AI-powered legal research',
      'Document generation',
      'Client communication tools',
      'Basic analytics',
      'Email support'
    ],
    limits: {
      users: 1,
      cases: 25,
      storage: 5,
      aiRequests: 1000
    }
  },
  {
    id: 'team',
    name: 'Team Plan',
    price: 199,
    currency: 'USD',
    popular: true,
    features: [
      'Everything in Solo',
      'Multi-user collaboration (up to 10 users)',
      'Advanced case management',
      'Team analytics',
      'Priority support',
      'Custom integrations',
      'Advanced document automation'
    ],
    limits: {
      users: 10,
      cases: 100,
      storage: 25,
      aiRequests: 5000
    }
  },
  {
    id: 'scale',
    name: 'Scale Plan',
    price: 399,
    currency: 'USD',
    features: [
      'Everything in Team',
      'Unlimited users',
      'Advanced automation workflows',
      'Custom reporting & analytics',
      'Dedicated account manager',
      'White-label options',
      'API access',
      'Advanced security features'
    ],
    limits: {
      users: -1, // Unlimited
      cases: -1, // Unlimited
      storage: 100,
      aiRequests: 25000
    }
  }
];

export default function UpgradePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
  const [currentPlan, setCurrentPlan] = useState<Plan | null>(null);
  const [showPaymentManager, setShowPaymentManager] = useState(false);
  const [loading, setLoading] = useState(true);
  
  // Mock user data - in real app, get from auth context
  const user = {
    tenantId: 'tenant-123',
    country: 'US' as const,
    currency: 'USD' as const
  };
  
  useEffect(() => {
    // Get current plan and pre-selected upgrade plan
    const upgradeParam = searchParams.get('plan');
    const fromParam = searchParams.get('from');
    
    // Mock current plan - in real app, fetch from API
    const mockCurrentPlan = PLANS.find(p => p.id === (fromParam || 'solo')) || PLANS[0];
    setCurrentPlan(mockCurrentPlan);
    
    // Pre-select upgrade plan if specified
    if (upgradeParam) {
      const targetPlan = PLANS.find(p => p.id === upgradeParam);
      if (targetPlan) {
        setSelectedPlan(targetPlan);
      }
    }
    
    setLoading(false);
  }, [searchParams]);
  
  const handlePlanSelection = (plan: Plan): void => {
    if (plan.id === currentPlan?.id) return; // Can't select current plan
    
    setSelectedPlan(plan);
    setShowPaymentManager(true);
  };
  
  const handlePaymentMethodCreated = (response: StripePaymentMethodCreationResponse): void => {
    // In real app, process the upgrade here
    console.log('Payment method created for upgrade:', response);
    
    // Redirect to success page
    router.push(`/subscription/success?plan=${selectedPlan?.id}&upgrade=true`);
  };
  
  const handlePaymentError = (error: Error): void => {
    console.error('Payment error:', error);
    // Redirect to error page
    router.push(`/payment/failed?plan=${selectedPlan?.id}&error=processing_error`);
  };
  
  const getPlanComparison = (plan: Plan) => {
    if (!currentPlan) return null;
    
    const currentIndex = PLANS.findIndex(p => p.id === currentPlan.id);
    const planIndex = PLANS.findIndex(p => p.id === plan.id);
    
    if (planIndex > currentIndex) {
      return { type: 'upgrade', label: 'Upgrade' };
    } else if (planIndex < currentIndex) {
      return { type: 'downgrade', label: 'Downgrade' };
    }
    return { type: 'current', label: 'Current Plan' };
  };
  
  const formatLimit = (value: number, unit: string) => {
    if (value === -1) return 'Unlimited';
    return `${value}${unit}`;
  };
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading plans...</p>
        </div>
      </div>
    );
  }
  
  if (showPaymentManager && selectedPlan) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Upgrade to {selectedPlan.name}
            </h1>
            <p className="text-gray-600">
              You're upgrading from <strong>{currentPlan?.name}</strong> to <strong>{selectedPlan.name}</strong>
            </p>
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-blue-800">
                <strong>New monthly charge:</strong> ${selectedPlan.price}/month
              </p>
              {currentPlan && (
                <p className="text-blue-600 text-sm mt-1">
                  Previous: ${currentPlan.price}/month • Difference: +${selectedPlan.price - currentPlan.price}/month
                </p>
              )}
            </div>
          </div>
          
          {/* Payment Method Manager */}
          <PaymentMethodManager
            tenantId={user.tenantId}
            countryCode={user.country}
            currencyCode={user.currency}
            amountCents={selectedPlan.price * 100}
            subscriptionPlan={selectedPlan.id}
            onPaymentMethodCreated={handlePaymentMethodCreated}
            onError={handlePaymentError}
            className="max-w-2xl mx-auto"
          />
          
          {/* Back Button */}
          <div className="text-center mt-8">
            <Button 
              variant="ghost" 
              onClick={() => setShowPaymentManager(false)}
            >
              ← Back to plan selection
            </Button>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Upgrade Your Plan
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {currentPlan && (
              <>You're currently on the <strong>{currentPlan.name}</strong>. </>
            )}
            Choose a plan that better fits your growing practice.
          </p>
        </div>
        
        {/* Current Plan Alert */}
        {currentPlan && (
          <Alert className="mb-8 max-w-2xl mx-auto">
            <TrendingUp className="h-4 w-4" />
            <AlertDescription>
              <strong>Current Plan:</strong> {currentPlan.name} (${currentPlan.price}/month)
              <br />
              Upgrade to unlock more features and higher limits for your growing practice.
            </AlertDescription>
          </Alert>
        )}
        
        {/* Plan Comparison */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {PLANS.map((plan) => {
            const comparison = getPlanComparison(plan);
            const isCurrentPlan = plan.id === currentPlan?.id;
            const isUpgrade = comparison?.type === 'upgrade';
            
            return (
              <Card 
                key={plan.id} 
                className={`relative ${plan.popular ? 'ring-2 ring-blue-500 shadow-lg' : ''} ${isCurrentPlan ? 'bg-gray-50 border-gray-300' : ''}`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-500 text-white px-4 py-1">
                      <Star className="h-3 w-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}
                
                {isCurrentPlan && (
                  <div className="absolute -top-3 right-4">
                    <Badge variant="secondary">
                      Current Plan
                    </Badge>
                  </div>
                )}
                
                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                  <div className="mt-4">
                    <span className="text-4xl font-bold">${plan.price}</span>
                    <span className="text-gray-600">/month</span>
                  </div>
                  
                  {/* Plan Limits */}
                  <div className="mt-4 grid grid-cols-2 gap-2 text-xs text-gray-600">
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {formatLimit(plan.limits.users, '')} users
                    </div>
                    <div className="flex items-center gap-1">
                      <Shield className="h-3 w-3" />
                      {formatLimit(plan.limits.cases, '')} cases
                    </div>
                    <div className="flex items-center gap-1">
                      <CreditCard className="h-3 w-3" />
                      {formatLimit(plan.limits.storage, 'GB')} storage
                    </div>
                    <div className="flex items-center gap-1">
                      <Zap className="h-3 w-3" />
                      {formatLimit(plan.limits.aiRequests, '')} AI requests
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  {/* Features */}
                  <ul className="space-y-3">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <Check className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  {/* CTA Button */}
                  <Button 
                    onClick={() => handlePlanSelection(plan)}
                    className={`w-full ${plan.popular ? 'bg-blue-600 hover:bg-blue-700' : ''}`}
                    size="lg"
                    disabled={isCurrentPlan}
                    variant={isCurrentPlan ? 'outline' : 'default'}
                  >
                    {isCurrentPlan ? (
                      'Current Plan'
                    ) : isUpgrade ? (
                      <>
                        Upgrade Now
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </>
                    ) : (
                      'Downgrade'
                    )}
                  </Button>
                  
                  {/* Upgrade Benefits */}
                  {isUpgrade && currentPlan && (
                    <div className="text-center text-sm text-green-600">
                      <Clock className="h-4 w-4 inline mr-1" />
                      Immediate access to new features
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
        
        {/* FAQ Section */}
        <div className="mt-16 max-w-3xl mx-auto">
          <h3 className="text-xl font-semibold text-gray-900 mb-6 text-center">
            Upgrade Questions
          </h3>
          
          <div className="space-y-4 text-sm">
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">When will I be charged?</h4>
              <p className="text-gray-600">
                Your upgrade takes effect immediately. You'll be charged the prorated difference for the current billing period, 
                and the full new amount on your next billing date.
              </p>
            </div>
            
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Can I downgrade later?</h4>
              <p className="text-gray-600">
                Yes, you can change your plan at any time. Downgrades take effect at the end of your current billing period 
                to ensure you don't lose access to features you've paid for.
              </p>
            </div>
            
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">What happens to my data?</h4>
              <p className="text-gray-600">
                All your data remains safe during plan changes. If you downgrade to a plan with lower limits, 
                you'll keep your existing data but won't be able to add more until you're within the new limits.
              </p>
            </div>
          </div>
        </div>
        
        {/* Support */}
        <div className="mt-12 text-center">
          <p className="text-gray-600 mb-4">
            Need help choosing the right plan?
          </p>
          <Button variant="outline">
            Contact Sales
          </Button>
        </div>
      </div>
    </div>
  );
}
