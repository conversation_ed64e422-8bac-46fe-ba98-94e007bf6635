import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server-new';
import { isAuthenticatedLegacy as isAuthenticated, hasRoleLegacy as hasRole, UserRole } from '@/lib/auth/server-exports';
import { logSecurityEvent } from '@/lib/security/forensics';

// Define testing interfaces
export interface TestCaseRequest {
  id?: string;
  name: string;
  description: string;
  agent_name: string;
  node_name?: string;
  input_data: Record<string, any>;
  expected_output?: Record<string, any>;
  success_criteria: string[];
  timeout_seconds?: number;
  tags?: string[];
  priority?: 'low' | 'medium' | 'high';
}

export interface TestExecutionRequest {
  agent_name: string;
  node_name?: string;
  test_case_ids?: string[];
  test_suite_name?: string;
  environment?: 'sandbox' | 'staging' | 'production';
}

export interface TestResult {
  test_case_id: string;
  agent_name: string;
  node_name?: string;
  success: boolean;
  score?: number;
  output?: Record<string, any>;
  execution_time_ms: number;
  token_usage?: Record<string, number>;
  error_message?: string;
  error_type?: string;
  timestamp: string;
  environment: string;
}

export interface TestSuiteResult {
  suite_name: string;
  agent_name: string;
  total_tests: number;
  passed_tests: number;
  failed_tests: number;
  success_rate: number;
  avg_execution_time_ms: number;
  avg_score: number;
  results: TestResult[];
  started_at: string;
  completed_at: string;
}

/**
 * GET /api/admin/agent-testing
 * Get test cases and results for agents
 */
export async function GET(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, [UserRole.Superadmin])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const agentName = searchParams.get('agent_name');
    const testSuite = searchParams.get('test_suite');
    const includeResults = searchParams.get('include_results') === 'true';

    // Mock test cases and results
    const mockTestCases = [
      {
        id: 'research_basic_test',
        name: 'Basic Legal Research Test',
        description: 'Test basic research functionality',
        agent_name: 'research_agent',
        input_data: {
          message: 'What is the statute of limitations for personal injury cases in Texas?'
        },
        expected_output: {
          type: 'research_response',
          contains: ['statute of limitations', 'Texas', 'personal injury']
        },
        success_criteria: [
          'Agent provides relevant information',
          'Response includes proper citations',
          'Mentions Texas-specific law'
        ],
        timeout_seconds: 30,
        tags: ['basic', 'research', 'statute_of_limitations'],
        priority: 'high' as const,
        is_enabled: true
      },
      {
        id: 'intake_pi_test',
        name: 'Personal Injury Intake Test',
        description: 'Test intake process for personal injury case',
        agent_name: 'intake_agent',
        input_data: {
          message: 'I was in a car accident last week and injured my back. The other driver ran a red light.'
        },
        expected_output: {
          practice_area: 'PERSONAL_INJURY',
          urgency: 'MEDIUM'
        },
        success_criteria: [
          'Correctly classifies as personal injury',
          'Gathers accident details',
          'Assesses appropriate urgency'
        ],
        timeout_seconds: 45,
        tags: ['intake', 'personal_injury', 'classification'],
        priority: 'high' as const,
        is_enabled: true
      }
    ];

    const mockTestResults = [
      {
        test_case_id: 'research_basic_test',
        agent_name: 'research_agent',
        success: true,
        score: 0.92,
        output: {
          response: 'In Texas, the statute of limitations for personal injury cases is generally 2 years...',
          citations: ['Tex. Civ. Prac. & Rem. Code § 16.003'],
          confidence: 0.95
        },
        execution_time_ms: 2340,
        token_usage: { input: 45, output: 120 },
        timestamp: '2025-01-10T10:30:00Z',
        environment: 'sandbox'
      },
      {
        test_case_id: 'intake_pi_test',
        agent_name: 'intake_agent',
        success: true,
        score: 0.88,
        output: {
          practice_area: 'PERSONAL_INJURY',
          urgency: 'MEDIUM',
          case_type: 'auto_accident'
        },
        execution_time_ms: 1890,
        token_usage: { input: 38, output: 85 },
        timestamp: '2025-01-10T10:32:00Z',
        environment: 'sandbox'
      }
    ];

    // Filter test cases by agent if specified
    let filteredTestCases = mockTestCases;
    if (agentName) {
      filteredTestCases = mockTestCases.filter(tc => tc.agent_name === agentName);
    }

    // Filter results by agent if specified
    let filteredResults = mockTestResults;
    if (agentName) {
      filteredResults = mockTestResults.filter(tr => tr.agent_name === agentName);
    }

    const response: any = {
      test_cases: filteredTestCases,
      test_suites: [
        {
          name: 'research_agent_suite',
          description: 'Test suite for research agent',
          agent_name: 'research_agent',
          test_count: 3
        },
        {
          name: 'intake_agent_suite',
          description: 'Test suite for intake agent',
          agent_name: 'intake_agent',
          test_count: 5
        }
      ]
    };

    if (includeResults) {
      response.test_results = filteredResults;
      response.summary = {
        total_tests: filteredResults.length,
        passed_tests: filteredResults.filter(r => r.success).length,
        failed_tests: filteredResults.filter(r => !r.success).length,
        avg_score: filteredResults.reduce((sum, r) => sum + (r.score || 0), 0) / filteredResults.length,
        avg_execution_time: filteredResults.reduce((sum, r) => sum + r.execution_time_ms, 0) / filteredResults.length
      };
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error in agent testing API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/admin/agent-testing
 * Execute tests for agents
 */
export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, [UserRole.Superadmin])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { action, ...requestData } = body;

    if (action === 'execute_tests') {
      const { agent_name, node_name, test_case_ids, environment = 'sandbox' } = requestData as TestExecutionRequest;

      if (!agent_name) {
        return NextResponse.json({
          error: 'agent_name is required for test execution'
        }, { status: 400 });
      }

      // Simulate test execution
      const executionId = `exec_${Date.now()}`;
      const startTime = new Date().toISOString();

      // Mock test execution results
      const mockResults: TestResult[] = [
        {
          test_case_id: test_case_ids?.[0] || 'mock_test',
          agent_name,
          node_name,
          success: true,
          score: 0.89,
          output: { mock: 'test result' },
          execution_time_ms: Math.floor(Math.random() * 3000) + 500,
          token_usage: { input: 42, output: 78 },
          timestamp: new Date().toISOString(),
          environment
        }
      ];

      // Log the security event
      await logSecurityEvent(supabase, 'agent_test.executed', {
        agent_name,
        node_name,
        test_case_ids,
        environment,
        execution_id: executionId,
        userId: user.id,
        userEmail: user.email
      });

      return NextResponse.json({
        success: true,
        execution_id: executionId,
        started_at: startTime,
        results: mockResults,
        summary: {
          total_tests: mockResults.length,
          passed_tests: mockResults.filter(r => r.success).length,
          failed_tests: mockResults.filter(r => !r.success).length,
          success_rate: mockResults.filter(r => r.success).length / mockResults.length
        }
      });

    } else if (action === 'create_test_case') {
      const testCase = requestData as TestCaseRequest;

      if (!testCase.name || !testCase.agent_name || !testCase.input_data) {
        return NextResponse.json({
          error: 'name, agent_name, and input_data are required'
        }, { status: 400 });
      }

      // Simulate creating test case
      const newTestCase = {
        id: `test_${Date.now()}`,
        ...testCase,
        created_at: new Date().toISOString(),
        created_by: user.email
      };

      // Log the security event
      await logSecurityEvent(supabase, 'test_case.created', {
        test_case_id: newTestCase.id,
        agent_name: testCase.agent_name,
        userId: user.id,
        userEmail: user.email
      });

      return NextResponse.json({
        success: true,
        test_case: newTestCase
      });

    } else {
      return NextResponse.json({
        error: 'Invalid action. Supported actions: execute_tests, create_test_case'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in agent testing POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
