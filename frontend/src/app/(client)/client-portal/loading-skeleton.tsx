import { Skeleton } from "@/components/ui/skeleton"

export function CasesSkeleton(): React.ReactElement {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-10 w-36" />
      </div>

      <div className="grid gap-6">
        {[1, 2].map((i) => (
          <div key={i} className="p-6 bg-white rounded-lg border">
            <div className="flex justify-between items-start">
              <div className="space-y-3">
                <Skeleton className="h-6 w-64" />
                <Skeleton className="h-4 w-96" />
              </div>
              <Skeleton className="h-6 w-16" />
            </div>
            <div className="mt-4 flex justify-between items-center">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
