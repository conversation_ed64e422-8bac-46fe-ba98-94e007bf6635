/**
 * Validation Services Unit Tests
 *
 * Comprehensive unit tests for payment validation rules engine,
 * regional validation services, and compliance validation systems.
 */

// Mock environment variables before any imports using hoisted
vi.hoisted(() => {
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://anwefmklplkjxkmzpnva.supabase.co';
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key';
  process.env.SENTRY_DSN = '';
  process.env.NODE_ENV = 'test';
});

// Mock Sentry to prevent module resolution issues
vi.mock('@sentry/nextjs', () => ({
  withSentryConfig: vi.fn((config) => config),
  captureException: vi.fn(),
  captureMessage: vi.fn(),
  withSentry: vi.fn((handler) => handler),
}));

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { RegionalPaymentMethodService } from '@/lib/services/regional-payment-method-service';
import { PaymentValidationRulesService } from '@/lib/services/payment-validation-rules-service';

// Mock dependencies using hoisted pattern
const mockSupabase = vi.hoisted(() => ({
  from: vi.fn().mockReturnValue({
    select: vi.fn().mockReturnValue({
      eq: vi.fn().mockReturnValue({
        single: vi.fn().mockResolvedValue({ data: null, _error: null })
      })
    })
  })
}));

vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn().mockReturnValue(mockSupabase)
}));

describe('Payment Validation Services', () => {
  let regionalService: RegionalPaymentMethodService;
  let validationService: PaymentValidationRulesService;

  beforeEach(() => {
    vi.clearAllMocks();
    regionalService = new RegionalPaymentMethodService();
    validationService = new PaymentValidationRulesService();
  });

  describe('Regional Payment Method Service', () => {
    describe('getAvailablePaymentMethods', () => {
      it('should return available payment methods for US/USD', async () => {
        const methods = await regionalService.getAvailablePaymentMethods('US', 'USD');
        
        expect(methods).toBeDefined();
        expect(Array.isArray(methods)).toBe(true);
        expect(methods.length).toBeGreaterThan(0);
        
        // Should include card payments for US
        const cardMethod = methods.find(m => m.payment_method_type.code === 'card');
        expect(cardMethod).toBeDefined();
        expect(cardMethod?.is_available).toBe(true);
      });

      it('should return SEPA methods for EU countries', async () => {
        const methods = await regionalService.getAvailablePaymentMethods('DE', 'EUR');
        
        expect(methods).toBeDefined();
        const sepaMethod = methods.find(m => m.payment_method_type.code === 'sepa_debit');
        expect(sepaMethod).toBeDefined();
        expect(sepaMethod?.is_available).toBe(true);
      });

      it('should filter methods by minimum amount', async () => {
        const methods = await regionalService.getAvailablePaymentMethods('US', 'USD', 100000); // $1000
        
        // High-value methods should be available
        expect(methods.length).toBeGreaterThan(0);
        
        methods.forEach(method => {
          if (method.regional_config.minimum_amount_cents) {
            expect(method.regional_config.minimum_amount_cents).toBeLessThanOrEqual(100000);
          }
        });
      });

      it('should handle unsupported country/currency combinations', async () => {
        const methods = await regionalService.getAvailablePaymentMethods('XX', 'XXX');
        expect(methods).toEqual([]);
      });
    });

    describe('validatePaymentMethod', () => {
      it('should validate US credit card data', async () => {
        const request = {
          payment_method_code: 'card',
          country_code: 'US',
          payment_data: {
            number: '****************',
            exp_month: 12,
            exp_year: 2025,
            cvc: '123'
          }
        };

        const result = await regionalService.validatePaymentMethod(request);

        expect(result.is_valid).toBe(true);
        expect(result.validation_errors).toHaveLength(0);
        expect(result.formatted_data).toBeDefined();
      });

      it('should validate SEPA IBAN', async () => {
        const request = {
          payment_method_code: 'sepa_debit',
          country_code: 'DE',
          payment_data: {
            iban: '**********************',
            account_holder_name: 'John Doe'
          }
        };

        const result = await regionalService.validatePaymentMethod(request);
        
        expect(result.is_valid).toBe(true);
        expect(result.validation_errors).toHaveLength(0);
        expect(result.formatted_data?.iban).toBe('**********************');
      });

      it('should reject invalid credit card numbers', async () => {
        const request = {
          payment_method_code: 'card',
          country_code: 'US',
          payment_data: {
            number: '****************', // Invalid card number
            exp_month: 12,
            exp_year: 2025,
            cvc: '123'
          }
        };

        const result = await regionalService.validatePaymentMethod(request);
        
        expect(result.is_valid).toBe(false);
        expect(result.validation_errors.length).toBeGreaterThan(0);
        expect(result.validation_errors[0].field).toBe('number');
        expect(result.validation_errors[0].code).toBe('INVALID_CARD_NUMBER');
      });

      it('should reject invalid IBAN', async () => {
        const request = {
          payment_method_code: 'sepa_debit',
          country_code: 'DE',
          payment_data: {
            iban: '**********************', // Invalid checksum
            account_holder_name: 'John Doe'
          }
        };

        const result = await regionalService.validatePaymentMethod(request);
        
        expect(result.is_valid).toBe(false);
        expect(result.validation_errors.length).toBeGreaterThan(0);
        expect(result.validation_errors[0].field).toBe('iban');
        expect(result.validation_errors[0].code).toBe('INVALID_IBAN');
      });

      it('should validate expiration dates', async () => {
        const pastDate = {
          payment_method_code: 'card',
          country_code: 'US',
          payment_data: {
            number: '****************',
            exp_month: 1,
            exp_year: 2020, // Past year
            cvc: '123'
          }
        };

        const result = await regionalService.validatePaymentMethod(pastDate);
        
        expect(result.is_valid).toBe(false);
        expect(result.validation_errors.some(e => e.code === 'EXPIRED_CARD')).toBe(true);
      });
    });
  });

  describe('Payment Validation Rules Service', () => {
    describe('Card Validation Rules', () => {
      it('should validate Visa card numbers', () => {
        const visaNumbers = [
          '****************',
          '****************',
          '****************'
        ];

        visaNumbers.forEach(number => {
          const result = validationService.validateCardNumber(number);
          expect(result.isValid).toBe(true);
          expect(result.brand).toBe('visa');
        });
      });

      it('should validate Mastercard numbers', () => {
        const mastercardNumbers = [
          '****************',
          '****************',
          '****************'
        ];

        mastercardNumbers.forEach(number => {
          const result = validationService.validateCardNumber(number);
          expect(result.isValid).toBe(true);
          expect(result.brand).toBe('mastercard');
        });
      });

      it('should validate American Express numbers', () => {
        const amexNumbers = [
          '***************',
          '***************',
          '***************'
        ];

        amexNumbers.forEach(number => {
          const result = validationService.validateCardNumber(number);
          expect(result.isValid).toBe(true);
          expect(result.brand).toBe('amex');
        });
      });

      it('should reject invalid card numbers', () => {
        const invalidNumbers = [
          '****************',
          '0000000000000000',
          '****************', // Wrong checksum
          '123' // Too short
        ];

        invalidNumbers.forEach(number => {
          const result = validationService.validateCardNumber(number);
          expect(result.isValid).toBe(false);
        });
      });

      it('should validate CVC codes', () => {
        // 3-digit CVC for most cards
        expect(validationService.validateCVC('123', 'visa')).toBe(true);
        expect(validationService.validateCVC('000', 'mastercard')).toBe(true);
        
        // 4-digit CVC for Amex
        expect(validationService.validateCVC('1234', 'amex')).toBe(true);
        
        // Invalid CVCs
        expect(validationService.validateCVC('12', 'visa')).toBe(false);
        expect(validationService.validateCVC('123', 'amex')).toBe(false);
        expect(validationService.validateCVC('abcd', 'visa')).toBe(false);
      });
    });

    describe('IBAN Validation Rules', () => {
      it('should validate correct IBAN formats', () => {
        const validIBANs = [
          '**********************', // Germany
          '**********************', // UK
          '***************************', // France
          '***************************', // Italy
          '************************' // Spain
        ];

        validIBANs.forEach(iban => {
          const result = validationService.validateIBAN(iban);
          expect(result.isValid).toBe(true);
          expect(result.country).toBeDefined();
        });
      });

      it('should reject invalid IBAN formats', () => {
        const invalidIBANs = [
          '**********************', // Wrong checksum
          'XX****************', // Invalid country
          '*********************', // Too short
          '**********************0', // Too long
          'DE89 3704 0044 0532 0130 00' // Spaces not allowed
        ];

        invalidIBANs.forEach(iban => {
          const result = validationService.validateIBAN(iban);
          expect(result.isValid).toBe(false);
        });
      });

      it('should extract country codes from IBANs', () => {
        const testCases = [
          { iban: '**********************', expectedCountry: 'DE' },
          { iban: '**********************', expectedCountry: 'GB' },
          { iban: '***************************', expectedCountry: 'FR' }
        ];

        testCases.forEach(({ iban, expectedCountry }) => {
          const result = validationService.validateIBAN(iban);
          expect(result.country).toBe(expectedCountry);
        });
      });
    });

    describe('Regional Validation Rules', () => {
      it('should apply US-specific validation rules', () => {
        const usRules = validationService.getRegionalRules('US');
        
        expect(usRules).toBeDefined();
        expect(usRules.supportedCardBrands).toContain('visa');
        expect(usRules.supportedCardBrands).toContain('mastercard');
        expect(usRules.supportedCardBrands).toContain('amex');
        expect(usRules.requiresZipCode).toBe(true);
      });

      it('should apply EU-specific validation rules', () => {
        const euCountries = ['DE', 'FR', 'IT', 'ES', 'NL'];
        
        euCountries.forEach(country => {
          const rules = validationService.getRegionalRules(country);
          expect(rules.supportsSepa).toBe(true);
          expect(rules.requiresIban).toBe(true);
        });
      });

      it('should handle unsupported regions', () => {
        const rules = validationService.getRegionalRules('XX');
        expect(rules.supportedCardBrands).toHaveLength(0);
        expect(rules.supportsSepa).toBe(false);
      });
    });

    describe('Amount Validation Rules', () => {
      it('should validate minimum amounts by region', () => {
        const testCases = [
          { country: 'US', currency: 'USD', amount: 50, shouldPass: true },
          { country: 'US', currency: 'USD', amount: 25, shouldPass: false },
          { country: 'DE', currency: 'EUR', amount: 100, shouldPass: true },
          { country: 'DE', currency: 'EUR', amount: 50, shouldPass: false }
        ];

        testCases.forEach(({ country, currency, amount, shouldPass }) => {
          const result = validationService.validateAmount(amount, country, currency);
          expect(result.isValid).toBe(shouldPass);
        });
      });

      it('should validate maximum amounts by region', () => {
        const testCases = [
          { country: 'US', currency: 'USD', amount: 999999, shouldPass: true },
          { country: 'US', currency: 'USD', amount: 1000000, shouldPass: false },
          { country: 'DE', currency: 'EUR', amount: 500000, shouldPass: true },
          { country: 'DE', currency: 'EUR', amount: 600000, shouldPass: false }
        ];

        testCases.forEach(({ country, currency, amount, shouldPass }) => {
          const result = validationService.validateAmount(amount, country, currency);
          expect(result.isValid).toBe(shouldPass);
        });
      });
    });

    describe('Address Validation Rules', () => {
      it('should validate US addresses', () => {
        const validAddress = {
          line1: '123 Main St',
          city: 'New York',
          state: 'NY',
          postal_code: '10001',
          country: 'US'
        };

        const result = validationService.validateAddress(validAddress);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it('should validate EU addresses', () => {
        const validAddress = {
          line1: 'Musterstraße 123',
          city: 'Berlin',
          postal_code: '10115',
          country: 'DE'
        };

        const result = validationService.validateAddress(validAddress);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it('should reject incomplete addresses', () => {
        const incompleteAddress = {
          line1: '123 Main St',
          country: 'US'
          // Missing city, state, postal_code
        };

        const result = validationService.validateAddress(incompleteAddress);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });

      it('should validate postal codes by country', () => {
        const testCases = [
          { country: 'US', postalCode: '10001', shouldPass: true },
          { country: 'US', postalCode: '10001-1234', shouldPass: true },
          { country: 'US', postalCode: '1234', shouldPass: false },
          { country: 'DE', postalCode: '10115', shouldPass: true },
          { country: 'DE', postalCode: '1234', shouldPass: false },
          { country: 'GB', postalCode: 'SW1A 1AA', shouldPass: true },
          { country: 'GB', postalCode: '12345', shouldPass: false }
        ];

        testCases.forEach(({ country, postalCode, shouldPass }) => {
          const result = validationService.validatePostalCode(postalCode, country);
          expect(result).toBe(shouldPass);
        });
      });
    });
  });
});
