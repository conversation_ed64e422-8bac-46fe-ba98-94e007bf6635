/**
 * Subscription Page
 * 
 * Professional subscription signup page with integrated payment method
 * selection for law firm customers.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, Shield, Lock, CreditCard } from 'lucide-react';
import { PaymentMethodManager } from '@/components/payment/PaymentMethodManager';
import type { StripePaymentMethodCreationResponse } from '@/lib/types/payment-methods';

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  features: string[];
  recommended?: boolean;
}

const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: 'solo',
    name: 'Solo Practitioner',
    price: 99,
    currency: 'USD',
    features: [
      'Personal Injury & Medical Malpractice',
      'AI-powered legal research',
      'Document generation',
      'Client communication tools',
      'Basic analytics'
    ]
  },
  {
    id: 'team',
    name: 'Team Plan',
    price: 199,
    currency: 'USD',
    recommended: true,
    features: [
      'All Solo features',
      'Multi-user collaboration',
      'Advanced case management',
      'Team analytics',
      'Priority support',
      'Custom integrations'
    ]
  },
  {
    id: 'scale',
    name: 'Scale Plan',
    price: 399,
    currency: 'USD',
    features: [
      'All Team features',
      'Unlimited users',
      'Advanced automation',
      'Custom workflows',
      'Dedicated account manager',
      'White-label options'
    ]
  }
];

export default function SubscribePage() {
  const router = useRouter();
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [showPaymentManager, setShowPaymentManager] = useState(false);

  // Mock user data - in real app, get from auth context
  const user = {
    tenantId: 'tenant-123',
    country: 'US' as const,
    currency: 'USD' as const
  };

  // Handle URL parameters for pre-selected plans
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const planParam = urlParams.get('plan');
    const currencyParam = urlParams.get('currency');
    const amountParam = urlParams.get('amount');

    if (planParam) {
      // Find matching plan
      const matchingPlan = SUBSCRIPTION_PLANS.find(plan =>
        plan.id === planParam || plan.name.toLowerCase().includes(planParam.toLowerCase())
      );

      if (matchingPlan) {
        // Update plan with URL parameters if provided
        const updatedPlan = {
          ...matchingPlan,
          currency: currencyParam || matchingPlan.currency,
          price: amountParam ? parseInt(amountParam) / 100 : matchingPlan.price
        };

        setSelectedPlan(updatedPlan);
        setShowPaymentManager(true);
      }
    }
  }, []);
  
  const handlePlanSelection = (plan: SubscriptionPlan): void => {
    setSelectedPlan(plan);
    setShowPaymentManager(true);
  };
  
  const handlePaymentMethodCreated = (response: StripePaymentMethodCreationResponse): void => {
    // In real app, create subscription here
    console.log('Payment method created:', response);
    
    // Redirect to success page
    router.push('/subscription/success');
  };
  
  const handlePaymentError = (error: Error): void => {
    console.error('Payment error:', error);
    // Handle error - show notification, etc.
  };
  
  if (showPaymentManager && selectedPlan) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Complete Your Subscription
            </h1>
            <p className="text-gray-600">
              You're subscribing to <strong>{selectedPlan.name}</strong> for ${selectedPlan.price}/month
            </p>
          </div>
          
          {/* Security Badges */}
          <div className="flex justify-center items-center gap-6 mb-8 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-green-600" />
              <span>Bank-level security</span>
            </div>
            <div className="flex items-center gap-2">
              <Lock className="h-4 w-4 text-green-600" />
              <span>256-bit SSL encryption</span>
            </div>
            <div className="flex items-center gap-2">
              <CreditCard className="h-4 w-4 text-green-600" />
              <span>Powered by Stripe</span>
            </div>
          </div>
          
          {/* Payment Method Manager */}
          <PaymentMethodManager
            tenantId={user.tenantId}
            countryCode={user.country}
            currencyCode={user.currency}
            amountCents={selectedPlan.price * 100}
            subscriptionPlan={selectedPlan.id}
            onPaymentMethodCreated={handlePaymentMethodCreated}
            onError={handlePaymentError}
            className="max-w-2xl mx-auto"
          />
          
          {/* Back Button */}
          <div className="text-center mt-8">
            <Button 
              variant="ghost" 
              onClick={() => setShowPaymentManager(false)}
            >
              ← Back to plan selection
            </Button>
          </div>
          
          {/* Trust Indicators */}
          <div className="mt-12 text-center text-sm text-gray-500">
            <p>Your payment information is encrypted and secure.</p>
            <p>Cancel anytime. No long-term contracts.</p>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Choose Your Plan
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Select the perfect plan for your law firm. Upgrade or downgrade anytime.
          </p>
        </div>
        
        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {SUBSCRIPTION_PLANS.map((plan) => (
            <Card 
              key={plan.id} 
              className={`relative ${plan.recommended ? 'ring-2 ring-blue-500 shadow-lg' : ''}`}
            >
              {plan.recommended && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-500 text-white px-4 py-1">
                    Most Popular
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold">${plan.price}</span>
                  <span className="text-gray-600">/month</span>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Features */}
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <Check className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                {/* CTA Button */}
                <Button 
                  onClick={() => handlePlanSelection(plan)}
                  className={`w-full ${plan.recommended ? 'bg-blue-600 hover:bg-blue-700' : ''}`}
                  size="lg"
                >
                  Get Started
                </Button>
                
                {/* Trial Info */}
                <p className="text-center text-sm text-gray-500">
                  14-day free trial • No setup fees
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {/* Trust Section */}
        <div className="mt-16 text-center">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">
            Trusted by 1,000+ Law Firms
          </h3>
          
          <div className="flex justify-center items-center gap-8 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-600" />
              <span>SOC 2 Type II Certified</span>
            </div>
            <div className="flex items-center gap-2">
              <Lock className="h-5 w-5 text-green-600" />
              <span>HIPAA Compliant</span>
            </div>
            <div className="flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-green-600" />
              <span>PCI DSS Level 1</span>
            </div>
          </div>
        </div>
        
        {/* FAQ Section */}
        <div className="mt-16 max-w-3xl mx-auto">
          <h3 className="text-xl font-semibold text-gray-900 mb-6 text-center">
            Frequently Asked Questions
          </h3>
          
          <div className="space-y-4 text-sm">
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Can I change plans later?</h4>
              <p className="text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
            </div>
            
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">What payment methods do you accept?</h4>
              <p className="text-gray-600">We accept all major credit cards, ACH bank transfers, and regional payment methods like SEPA.</p>
            </div>
            
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Is my data secure?</h4>
              <p className="text-gray-600">Absolutely. We use bank-level encryption and are SOC 2 Type II certified with HIPAA compliance.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
