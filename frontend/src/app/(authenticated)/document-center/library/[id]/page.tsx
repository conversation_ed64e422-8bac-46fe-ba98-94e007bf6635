'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { notFound } from 'next/navigation';
import { DocumentSummary } from '@/components/proactive/DocumentSummary';
import type { DocumentViewerWithHighlightsHandle } from '@/components/proactive/DocumentViewerWithHighlights';
import { DocumentViewerWithHighlights } from '@/components/proactive/DocumentViewerWithHighlights';
import { DocumentHighlightsSidebar } from '@/components/proactive/DocumentHighlightsSidebar';
import { useUser } from '@/contexts/UserContext';

async function fetchDocumentSummary(documentId: string) {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL || ''}/api/documents/summary?document_id=${encodeURIComponent(documentId)}`,
      { cache: 'no-store' }
    );
    if (!res.ok) {
      if (res.status === 404) return null;
      throw new Error((await res.json()).error || 'Failed to fetch summary');
    }
    return await res.json();
  } catch (e) {
    return null;
  }
}

// Placeholder: replace with real API call for document text
async function fetchDocumentText(documentId: string): Promise<string> {
  // TODO: Replace with real API call
  return 'This is the full original document text. Replace this with the actual loaded document.';
}

interface Highlight {
  id: string;
  start_offset: number;
  end_offset: number;
  highlight_color?: string;
  comment?: string;
  created_by: string;
  created_at: string;
}

interface DocumentDetailPageProps {
  params: { id: string };
}

export default function DocumentDetailPage({ params }: DocumentDetailPageProps) {
  const { id } = params;
  const { user } = useUser();
  const [summary, setSummary] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [docText, setDocText] = useState<string>('');
  const [docLoading, setDocLoading] = useState(true);
  const [highlights, setHighlights] = useState<Highlight[]>([]);
  const [selectedHighlightId, setSelectedHighlightId] = useState<string | null>(null);
  const viewerRef = useRef<DocumentViewerWithHighlightsHandle>(null);

  const loadSummary = useCallback(async () => {
    setLoading(true);
    const result = await fetchDocumentSummary(id);
    setSummary(result);
    setLoading(false);
  }, [id]);

  useEffect(() => {
    loadSummary();
    setDocLoading(true);
    fetchDocumentText(id).then(text => {
      setDocText(text);
      setDocLoading(false);
    });
  }, [loadSummary, id]);

  const handleJumpToHighlight = (hl: Highlight): void => {
    setSelectedHighlightId(hl.id);
    viewerRef.current?.scrollToHighlight(hl.id);
    // Optionally, clear after a delay
    setTimeout(() => setSelectedHighlightId(null), 1500);
  };

  return (
    <main className="max-w-4xl mx-auto py-12 px-4 flex flex-col md:flex-row gap-8">
      <section className="flex-1">
        <h1 className="text-2xl font-bold mb-6">Document Detail: {id}</h1>
        {docLoading ? (
          <div className="my-6 text-center text-muted-foreground">Loading document...</div>
        ) : (
          <DocumentViewerWithHighlights
            ref={viewerRef}
            documentId={id}
            documentType={summary?.documentType || 'authored'}
            text={docText}
            canEdit={true} // TODO: use role check
            highlightId={selectedHighlightId}
            onHighlightsLoaded={setHighlights}
          />
        )}
        <div className="mt-8" />
        {loading ? (
          <div className="my-6 text-center text-muted-foreground">Loading summary...</div>
        ) : summary ? (
          <DocumentSummary
            documentId={id}
            documentType={summary.documentType}
            summaryText={summary.summaryText}
            summarizedBy={summary.summarizedBy}
            summarizedAt={summary.summarizedAt}
            onSummaryUpdated={loadSummary}
          />
        ) : (
          <div className="my-6 p-6 rounded border border-dashed border-gray-300 bg-gray-50 text-gray-500 text-center">
            No summary is available for this document yet.
          </div>
        )}
      </section>
      <DocumentHighlightsSidebar
        highlights={highlights}
        userId={user?.id}
        text={docText}
        onJumpTo={handleJumpToHighlight}
      />
    </main>
  );
}
