//frontend/src/app/(client)/client-portal/submit-case/page.tsx
'use client'

import { ErrorBoundary } from 'react-error-boundary'
import { useSupabase } from '@/lib/supabase/provider'
import { Button } from "@/components/ui/button"
import Link from 'next/link'
import { CopilotChat } from "@copilotkit/react-ui"
import { ProgressWidget } from "@/components/progress/ProgressWidget"
import { useCoAgent } from '@copilotkit/react-core'

export default function SubmitCasePage() {
  const { supabase } = useSupabase()

  const handleError = (error: Error): void => {
    console.error('CopilotKit Error:', error)
  }

  // Use useCoAgent to get the state for the intake_agent
  const { state } = useCoAgent({ name: "intake_agent" })

  return (
    <div className="container max-w-5xl mx-auto py-8 px-4">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Submit New Case</h1>
          <p className="text-muted-foreground mt-1">
            Our AI assistant will guide you through the case submission process
          </p>
        </div>
        <Link href="/client-portal">
          <Button variant="outline">← Back to Cases</Button>
        </Link>
      </div>

      <div className="flex gap-4">
        {/* Chat interface */}
        <div className="flex-1">
          <ErrorBoundary fallback={<div>Something went wrong</div>} onError={handleError}>
            <CopilotChat
              className="w-full h-[700px] border rounded-lg bg-background"
              instructions="You are a legal intake assistant for personal injury cases. Help gather relevant information from potential clients while being empathetic and professional."
              labels={{
                title: "Legal Intake Assistant",
                initial: "Hi! 👋 I'm AiLex here to help you with your personal injury case. Could you tell me what happened?",
              }}
            />
          </ErrorBoundary>
        </div>

        {/* Progress widget rendered conditionally using the agent state */}
        <div className="w-[300px] shrink-0">
          { state?.show_progress_widget && <ProgressWidget /> }
        </div>
      </div>
    </div>
  )
}
