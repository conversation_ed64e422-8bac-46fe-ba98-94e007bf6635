/**
 * Payment Failed Page
 * 
 * Error page shown when payment processing fails
 * with recovery options and support information.
 */

'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  XCircle, 
  ArrowLeft, 
  CreditCard, 
  RefreshCw, 
  Mail, 
  Phone,
  AlertTriangle,
  HelpCircle
} from 'lucide-react';

interface PaymentError {
  code: string;
  message: string;
  type: 'card_error' | 'payment_error' | 'validation_error' | 'api_error';
  suggestion?: string;
}

const COMMON_ERRORS: Record<string, PaymentError> = {
  'card_declined': {
    code: 'card_declined',
    message: 'Your card was declined',
    type: 'card_error',
    suggestion: 'Please try a different payment method or contact your bank.'
  },
  'insufficient_funds': {
    code: 'insufficient_funds',
    message: 'Insufficient funds',
    type: 'card_error',
    suggestion: 'Please ensure your account has sufficient funds or try a different card.'
  },
  'expired_card': {
    code: 'expired_card',
    message: 'Your card has expired',
    type: 'card_error',
    suggestion: 'Please update your card information with a valid expiration date.'
  },
  'incorrect_cvc': {
    code: 'incorrect_cvc',
    message: 'Your card\'s security code is incorrect',
    type: 'card_error',
    suggestion: 'Please check the 3-digit security code on the back of your card.'
  },
  'processing_error': {
    code: 'processing_error',
    message: 'Payment processing error',
    type: 'payment_error',
    suggestion: 'Please try again in a few minutes or contact support if the issue persists.'
  },
  'authentication_required': {
    code: 'authentication_required',
    message: 'Additional authentication required',
    type: 'card_error',
    suggestion: 'Your bank requires additional verification. Please complete the authentication process.'
  }
};

export default function PaymentFailedPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [paymentError, setPaymentError] = useState<PaymentError | null>(null);
  const [retryAttempts, setRetryAttempts] = useState(0);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Get error details from URL parameters
    const errorCode = searchParams.get('error') || 'processing_error';
    const errorMessage = searchParams.get('message');
    const planId = searchParams.get('plan');
    
    // Set error details
    const error = COMMON_ERRORS[errorCode] || {
      code: errorCode,
      message: errorMessage || 'Payment processing failed',
      type: 'payment_error' as const,
      suggestion: 'Please try again or contact support for assistance.'
    };
    
    setPaymentError(error);
    setLoading(false);
  }, [searchParams]);
  
  const handleRetryPayment = (): void => {
    const planId = searchParams.get('plan');
    const amount = searchParams.get('amount');
    
    // Increment retry attempts
    setRetryAttempts(prev => prev + 1);
    
    // Redirect back to subscription page with plan pre-selected
    const params = new URLSearchParams();
    if (planId) params.set('plan', planId);
    if (amount) params.set('amount', amount);
    params.set('retry', 'true');
    
    router.push(`/subscribe?${params.toString()}`);
  };
  
  const handleTryDifferentMethod = (): void => {
    const planId = searchParams.get('plan');
    const amount = searchParams.get('amount');
    
    // Redirect to subscription page with different payment method flag
    const params = new URLSearchParams();
    if (planId) params.set('plan', planId);
    if (amount) params.set('amount', amount);
    params.set('change_method', 'true');
    
    router.push(`/subscribe?${params.toString()}`);
  };
  
  const getErrorIcon = () => {
    if (paymentError?.type === 'card_error') {
      return <CreditCard className="h-8 w-8 text-red-600" />;
    }
    return <XCircle className="h-8 w-8 text-red-600" />;
  };
  
  const getErrorSeverity = () => {
    if (paymentError?.code === 'card_declined' || paymentError?.code === 'insufficient_funds') {
      return 'high';
    }
    if (paymentError?.code === 'expired_card' || paymentError?.code === 'incorrect_cvc') {
      return 'medium';
    }
    return 'low';
  };
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading error details...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4">
        {/* Error Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-6">
            {getErrorIcon()}
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Payment Failed
          </h1>
          
          <p className="text-lg text-gray-600">
            We couldn't process your payment. Don't worry, no charges were made to your account.
          </p>
        </div>
        
        {/* Error Details */}
        {paymentError && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-700">
                <AlertTriangle className="h-5 w-5" />
                Error Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">What happened?</h3>
                <p className="text-gray-700">{paymentError.message}</p>
              </div>
              
              {paymentError.suggestion && (
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">How to fix it:</h3>
                  <p className="text-gray-700">{paymentError.suggestion}</p>
                </div>
              )}
              
              <Alert variant={getErrorSeverity() === 'high' ? 'destructive' : 'default'}>
                <HelpCircle className="h-4 w-4" />
                <AlertDescription>
                  {getErrorSeverity() === 'high' && (
                    'This appears to be a card-related issue. Please contact your bank or try a different payment method.'
                  )}
                  {getErrorSeverity() === 'medium' && (
                    'Please check your payment information and try again.'
                  )}
                  {getErrorSeverity() === 'low' && (
                    'This was likely a temporary issue. Please try again.'
                  )}
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        )}
        
        {/* Recovery Options */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>What would you like to do?</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button 
                onClick={handleRetryPayment}
                className="h-auto p-4 flex flex-col items-start"
                variant="outline"
              >
                <div className="flex items-center gap-2 mb-2">
                  <RefreshCw className="h-5 w-5" />
                  <span className="font-medium">Try Again</span>
                </div>
                <span className="text-sm text-gray-600 text-left">
                  Retry the payment with the same information
                </span>
              </Button>
              
              <Button 
                onClick={handleTryDifferentMethod}
                className="h-auto p-4 flex flex-col items-start"
                variant="outline"
              >
                <div className="flex items-center gap-2 mb-2">
                  <CreditCard className="h-5 w-5" />
                  <span className="font-medium">Different Payment Method</span>
                </div>
                <span className="text-sm text-gray-600 text-left">
                  Try with a different card or payment method
                </span>
              </Button>
            </div>
            
            {retryAttempts > 2 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  You've tried multiple times. Consider contacting support for assistance or trying a different payment method.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
        
        {/* Support Options */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Need Help?</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="text-center">
                <Mail className="h-8 w-8 text-blue-600 mx-auto mb-3" />
                <h3 className="font-medium text-gray-900 mb-2">Email Support</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Get help from our billing specialists
                </p>
                <Button variant="outline" size="sm">
                  <EMAIL>
                </Button>
              </div>
              
              <div className="text-center">
                <Phone className="h-8 w-8 text-blue-600 mx-auto mb-3" />
                <h3 className="font-medium text-gray-900 mb-2">Phone Support</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Speak with a support representative
                </p>
                <Button variant="outline" size="sm">
                  1-800-PI-LAWYER
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Common Issues */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Common Payment Issues</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Card Declined</h4>
                <p className="text-gray-600">
                  Contact your bank to ensure international transactions are enabled and your card isn't blocked.
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Insufficient Funds</h4>
                <p className="text-gray-600">
                  Ensure your account has sufficient balance or credit limit for the transaction.
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Expired Card</h4>
                <p className="text-gray-600">
                  Update your payment method with a card that has a valid expiration date.
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Security Code Error</h4>
                <p className="text-gray-600">
                  Double-check the 3-digit security code (CVV) on the back of your card.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Back Button */}
        <div className="text-center">
          <Button 
            variant="ghost"
            onClick={() => router.push('/dashboard')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
}
