'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import {
  FileUp,
  Search,
  FolderOpen,
  FileText,
  Clock,
  Pie<PERSON>hart,
  AlertCircle
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card'
import { useSupabase } from '@/lib/supabase/provider'
import { useToast } from '@/components/ui/use-toast'

// Sample data - would be replaced with actual data from Supabase
const mockStats = {
  totalDocuments: 47,
  recentUploads: 12,
  processingQueue: 3,
  documentTypes: {
    medical: 15,
    legal: 10,
    correspondence: 8,
    insurance: 7,
    other: 7
  }
}

export default function DocumentCenterPage() {
  const router = useRouter()
  const { supabase } = useSupabase()
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState('')
  const [stats, setStats] = useState(mockStats)
  const [recentDocuments, setRecentDocuments] = useState<any[]>([])

  // Fetch document stats and recent documents
  useEffect(() => {
    const fetchData = async () => {
      try {
        // In production, this would fetch real stats from the API
        // const response = await fetch('/api/documents/stats')
        // const data = await response.json()
        // setStats(data)

        // For now, we'll use mock data
        setRecentDocuments([
          {
            id: 'doc1',
            title: 'Medical Records - Johnson',
            type: 'medical',
            uploadedAt: '2025-04-01T10:30:00Z',
            processingStatus: 'completed'
          },
          {
            id: 'doc2',
            title: 'Insurance Claim Form',
            type: 'insurance',
            uploadedAt: '2025-04-03T14:45:00Z',
            processingStatus: 'completed'
          },
          {
            id: 'doc3',
            title: 'Police Report - Smith Accident',
            type: 'police',
            uploadedAt: '2025-04-05T09:15:00Z',
            processingStatus: 'processing'
          }
        ])
      } catch (error) {
        console.error('Error fetching document data:', error)
        toast({
          title: 'Error',
          description: 'Failed to load document information',
          variant: 'destructive'
        })
      }
    }

    fetchData()
  }, [toast])

  // Handle search
  const handleSearch = (): void => {
    if (searchQuery.trim()) {
      router.push(`/document-center/library?search=${encodeURIComponent(searchQuery)}`)
    }
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  return (
    <div className="container max-w-7xl mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Document Center</h1>
        <p className="text-muted-foreground mt-1">
          Upload, process, and manage all case-related documents
        </p>
      </div>

      {/* Quick Search */}
      <div className="mb-8">
        <div className="relative max-w-md">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search documents..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
          />
        </div>
      </div>

      {/* Main Action Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <FileUp className="mr-2 h-5 w-5 text-primary" />
              Upload Documents
            </CardTitle>
            <CardDescription>
              Upload and process new documents
            </CardDescription>
          </CardHeader>
          <CardContent className="text-sm text-muted-foreground">
            <p>Upload files individually or in batches</p>
            <p>Automatic text extraction and analysis</p>
            <p>Link documents to cases or clients</p>
          </CardContent>
          <CardFooter>
            <Button
              variant="default"
              className="w-full"
              onClick={() => router.push('/document-center/upload')}
            >
              Upload New Documents
            </Button>
          </CardFooter>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <FolderOpen className="mr-2 h-5 w-5 text-primary" />
              Document Library
            </CardTitle>
            <CardDescription>
              Browse and search your documents
            </CardDescription>
          </CardHeader>
          <CardContent className="text-sm text-muted-foreground">
            <p>Access all uploaded documents</p>
            <p>Filter by type, date, or client</p>
            <p>View extracted text and metadata</p>
          </CardContent>
          <CardFooter>
            <Button
              variant="default"
              className="w-full"
              onClick={() => router.push('/document-center/library')}
            >
              Browse Documents
            </Button>
          </CardFooter>
        </Card>

        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5 text-primary" />
              Processing Status
            </CardTitle>
            <CardDescription>
              Monitor document processing
            </CardDescription>
          </CardHeader>
          <CardContent className="text-sm text-muted-foreground">
            <p>Track ongoing document processing</p>
            <p>View processing history and logs</p>
            <p>Retry failed processing jobs</p>
          </CardContent>
          <CardFooter>
            <Button
              variant="default"
              className="w-full"
              onClick={() => router.push('/document-center/processing')}
            >
              View Processing Queue
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Stats Row */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalDocuments}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Across all cases
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Recent Uploads</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.recentUploads}</div>
            <p className="text-xs text-muted-foreground mt-1">
              In the last 30 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Processing Queue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.processingQueue}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Documents being analyzed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Document Types</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(stats.documentTypes).length}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Different document categories
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Documents */}
      <Card>
        <CardHeader>
          <CardTitle>Recently Uploaded Documents</CardTitle>
          <CardDescription>
            Most recent document uploads and their processing status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentDocuments.map(doc => (
              <div
                key={doc.id}
                className="flex items-center justify-between border-b pb-3 last:border-0"
                onClick={() => router.push(`/document-center/library/${doc.id}`)}
              >
                <div className="flex items-start gap-3">
                  <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="font-medium">{doc.title}</p>
                    <p className="text-sm text-muted-foreground">
                      Uploaded {formatDate(doc.uploadedAt)} • Type: <span className="capitalize">{doc.type}</span>
                    </p>
                  </div>
                </div>
                <div>
                  {doc.processingStatus === 'processing' ? (
                    <div className="flex items-center text-sm text-amber-600">
                      <Clock className="h-3 w-3 mr-1" />
                      Processing
                    </div>
                  ) : doc.processingStatus === 'error' ? (
                    <div className="flex items-center text-sm text-destructive">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      Failed
                    </div>
                  ) : (
                    <div className="text-sm text-green-600">Completed</div>
                  )}
                </div>
              </div>
            ))}

            {recentDocuments.length === 0 && (
              <div className="text-center py-6 text-muted-foreground">
                <p>No documents uploaded yet</p>
                <Button
                  variant="link"
                  className="mt-2"
                  onClick={() => router.push('/document-center/upload')}
                >
                  Upload your first document
                </Button>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => router.push('/document-center/library')}
          >
            View All Documents
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
