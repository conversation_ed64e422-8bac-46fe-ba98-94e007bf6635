'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { <PERSON><PERSON>p, ArrowLeft, Check } from 'lucide-react'
import { DocumentUpload } from '@/components/documents/document-upload'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { useToast } from '@/components/ui/use-toast'
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'

export default function DocumentUploadPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [uploadSuccess, setUploadSuccess] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([])

  // Handle upload completion
  const handleUploadComplete = (files: unknown[]): void => {
    setUploadedFiles(files)
    setUploadSuccess(true)

    toast({
      title: 'Upload Successful',
      description: `${files.length} document${files.length === 1 ? '' : 's'} uploaded successfully.`,
      variant: 'default',
    })
  }

  // Reset for new upload
  const handleNewUpload = (): void => {
    setUploadSuccess(false)
    setUploadedFiles([])
  }

  return (
    <div className="container max-w-5xl mx-auto py-8 px-4">
      {/* Breadcrumb */}
      <div className="mb-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/document-center">Document Center</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/document-center/upload">Upload</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Upload Documents</h1>
          <p className="text-muted-foreground mt-1">
            Upload and process documents for your cases
          </p>
        </div>
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
      </div>

      {uploadSuccess ? (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-green-600">
              <Check className="mr-2 h-5 w-5" />
              Upload Complete
            </CardTitle>
            <CardDescription>
              Your documents were uploaded successfully and are now being processed
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-6">
              <Alert>
                <AlertTitle>Processing in background</AlertTitle>
                <AlertDescription>
                  Your documents are being analyzed to extract key information. This process happens automatically and you'll be notified when it's complete.
                </AlertDescription>
              </Alert>
            </div>

            <div className="flex gap-4">
              <Button
                variant="default"
                onClick={() => router.push('/document-center/processing')}
              >
                View Processing Status
              </Button>
              <Button
                variant="outline"
                onClick={handleNewUpload}
              >
                Upload More Documents
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileUp className="mr-2 h-5 w-5" />
              Document Upload
            </CardTitle>
            <CardDescription>
              Drag and drop files or click to browse
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DocumentUpload
              onUploadComplete={handleUploadComplete}
              maxFiles={20}
              maxSize={100} // 100MB
              enableOcrByDefault={true}
              defaultOcrType="text"
            />
          </CardContent>
        </Card>
      )}

      {/* Document Processing Information */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">What happens after upload?</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">1. Document Analysis</CardTitle>
            </CardHeader>
            <CardContent className="text-sm">
              <p>Advanced OCR extracts all text from your documents</p>
              <p className="text-muted-foreground mt-1">
                Works with PDFs, images, and scanned documents
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">2. Information Extraction</CardTitle>
            </CardHeader>
            <CardContent className="text-sm">
              <p>AI identifies key information like dates, parties, and deadlines</p>
              <p className="text-muted-foreground mt-1">
                Creates searchable metadata for easy retrieval
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">3. Integration</CardTitle>
            </CardHeader>
            <CardContent className="text-sm">
              <p>Documents are linked to relevant cases and clients</p>
              <p className="text-muted-foreground mt-1">
                Supporting information becomes instantly accessible
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
