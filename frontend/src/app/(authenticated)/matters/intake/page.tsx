'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { toast } from 'sonner'

import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { MultiPracticeIntake } from "@/components/intake/multi-practice-intake"
import { StaffIntakeForm } from "@/components/intake/staff-intake-form"

export default function IntakePage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('ai-assisted')

  const handleIntakeComplete = (result: any): void => {
    toast.success('Intake completed successfully!')
    
    // Navigate to the appropriate page based on the result
    if (result.matter?.practice_area) {
      const practiceArea = result.matter.practice_area
      const displayLabel = result.matter.display_label || 'Matter'
      
      toast.success(`${displayLabel} created successfully for ${practiceArea.replace('_', ' ')} practice area`)
      
      // Navigate to matters list or specific matter page
      router.push('/matters')
    } else {
      router.push('/matters')
    }
  }

  return (
    <div className="container max-w-7xl mx-auto py-8 px-4">
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">New Client Intake</h1>
          <p className="text-muted-foreground mt-1">
            Multi-practice intake for Personal Injury, Family Law, and Criminal Defense
          </p>
        </div>
        <div className="flex space-x-2">
          <Link href="/matters">
            <Button variant="outline">← Back to Matters</Button>
          </Link>
          <Link href="/clients">
            <Button variant="outline">View Clients</Button>
          </Link>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="ai-assisted">AI Assisted Intake</TabsTrigger>
          <TabsTrigger value="quick-form">Quick Form Entry</TabsTrigger>
        </TabsList>

        <TabsContent value="ai-assisted" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>AI-Assisted Intake</CardTitle>
              <CardDescription>
                Our AI assistant will guide you through the intake process, automatically 
                classifying the practice area and collecting the appropriate information 
                for Personal Injury, Family Law, or Criminal Defense cases.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <MultiPracticeIntake 
                mode="staff" 
                onComplete={handleIntakeComplete}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="quick-form" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Quick Form Entry</CardTitle>
              <CardDescription>
                Use the traditional form interface for faster data entry when you 
                have all the client information available.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <StaffIntakeForm />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Help Section */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="text-lg">Intake Guidelines</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-semibold text-primary mb-2">Personal Injury Cases</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Auto accidents and collisions</li>
                <li>• Slip and fall incidents</li>
                <li>• Medical malpractice</li>
                <li>• Product liability</li>
                <li>• Workplace injuries</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-primary mb-2">Family Law Matters</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Divorce proceedings</li>
                <li>• Child custody disputes</li>
                <li>• Child support cases</li>
                <li>• Adoption processes</li>
                <li>• Domestic violence protection</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-primary mb-2">Criminal Defense Cases</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• DUI/DWI charges</li>
                <li>• Misdemeanor offenses</li>
                <li>• Felony charges</li>
                <li>• Traffic violations</li>
                <li>• White collar crimes</li>
              </ul>
              <p className="text-xs text-destructive mt-2">
                ⚠️ Criminal cases are time-sensitive and require immediate attention
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
