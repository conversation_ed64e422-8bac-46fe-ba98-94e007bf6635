import type { ReactNode } from "react";
import { CopilotKit } from "@copilotkit/react-core";

export default function DocumentsLayout({ children }: { children: ReactNode }) {
  return (
    <CopilotKit
      runtimeUrl="/api/copilotkit"
      agent="document_agent"  // Lock to document agent
      properties={{
        userRole: 'staff',
        context: 'documents',
        // In production, use deterministic ID based on user + org
        threadId: `document-${crypto.randomUUID()}`
      }}
    >
      <div className="documents-layout">
        {children}
      </div>
    </CopilotKit>
  );
}
