import type { ReactNode } from "react";
import { CopilotKit } from "@copilotkit/react-core";

export default function IntakeLayout({ children }: { children: ReactNode }) {
  return (
    <CopilotKit
      runtimeUrl="/api/copilotkit"
      agent="intake_agent"  // Lock to intake agent
      properties={{
        userRole: 'staff',
        context: 'intake',
        // Use a more deterministic ID in production
        threadId: `intake-staff-${Date.now().toString()}`
      }}
    >
      <div className="intake-layout">
        {children}
      </div>
    </CopilotKit>
  );
}
