'use client';

import { useState, useEffect } from 'react';
import { useSupabase } from '@/lib/supabase/provider';
import { useUser } from '@/contexts/UserContext';
import type { Notification, NotificationType } from '@/lib/services/notification-service';
import { NotificationService } from '@/lib/services/notification-service';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Bell, Check, AlertTriangle, CreditCard, BarChart, Shield, Info } from 'lucide-react';
import { format } from 'date-fns';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

export default function NotificationHistoryPage() {
  const { supabase } = useSupabase();
  const { user } = useUser();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const { toast } = useToast();

  useEffect(() => {
    if (!user || !supabase) return; // Guard against null user or supabase client

    const notificationService = new NotificationService(supabase);

    async function fetchNotifications() {
      try {
        setLoading(true);

        if (!user) {
          throw new Error("User not available");
        }

        const fetchedNotifications = await notificationService.getUserNotifications(
          user.id,
          true, // Include read notifications
          100 // Get up to 100 notifications
        );

        setNotifications(fetchedNotifications);
      } catch (error) {
        console.error('Error fetching notifications:', error);
        toast({
          title: 'Error',
          description: 'Failed to load notifications',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchNotifications();
  }, [supabase, user, toast]); // Depend on the user object

  const handleMarkAsRead = async (notificationId: string) => {
    if (!user) return;

    try {
      const notificationService = new NotificationService(supabase);
      await notificationService.markAsRead(notificationId, user.id);

      // Update the local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, read: true }
            : notification
        )
      );

      toast({
        title: 'Notification Marked as Read',
        description: 'The notification has been marked as read.',
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast({
        title: 'Error',
        description: 'Failed to mark notification as read',
        variant: 'destructive',
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!user) return;

    try {
      const notificationService = new NotificationService(supabase);
      await notificationService.markAllAsRead(user.id);

      // Update the local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, read: true }))
      );

      toast({
        title: 'All Notifications Marked as Read',
        description: 'All notifications have been marked as read.',
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast({
        title: 'Error',
        description: 'Failed to mark all notifications as read',
        variant: 'destructive',
      });
    }
  };

  const getNotificationIcon = (type: NotificationType, severity: string) => {
    switch (type) {
      case 'subscription_trial_ending':
      case 'subscription_payment_failed':
      case 'subscription_renewed':
        return <CreditCard className={cn(
          'h-5 w-5',
          severity === 'high' ? 'text-red-500' :
          severity === 'medium' ? 'text-amber-500' :
          'text-blue-500'
        )} />;

      case 'quota_limit_approaching':
      case 'quota_limit_reached':
        return <BarChart className={cn(
          'h-5 w-5',
          severity === 'high' ? 'text-red-500' :
          severity === 'medium' ? 'text-amber-500' :
          'text-blue-500'
        )} />;

      case 'security_alert':
        return <Shield className="h-5 w-5 text-red-500" />;

      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const getFilteredNotifications = () => {
    let filtered = notifications;

    // Apply read/unread filter
    if (filter === 'unread') {
      filtered = filtered.filter(notification => !notification.read);
    } else if (filter === 'read') {
      filtered = filtered.filter(notification => notification.read);
    }

    // Apply type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(notification => {
        if (typeFilter === 'subscription') {
          return ['subscription_trial_ending', 'subscription_payment_failed', 'subscription_renewed'].includes(notification.type);
        } else if (typeFilter === 'quota') {
          return ['quota_limit_approaching', 'quota_limit_reached'].includes(notification.type);
        } else if (typeFilter === 'security') {
          return notification.type === 'security_alert';
        } else if (typeFilter === 'system') {
          return notification.type === 'system_message';
        }
        return false;
      });
    }

    return filtered;
  };

  const filteredNotifications = getFilteredNotifications();
  const unreadCount = notifications.filter(notification => !notification.read).length;

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Notification History</h1>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Notifications</CardTitle>
              <CardDescription>
                View and manage your notifications
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="subscription">Subscription</SelectItem>
                  <SelectItem value="quota">Quota & Usage</SelectItem>
                  <SelectItem value="security">Security</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                </SelectContent>
              </Select>

              {unreadCount > 0 && (
                <Button variant="outline" onClick={handleMarkAllAsRead}>
                  <Check className="h-4 w-4 mr-2" />
                  Mark All as Read
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" onValueChange={(value) => setFilter(value as 'all' | 'unread' | 'read')}>
            <TabsList className="mb-4">
              <TabsTrigger value="all">
                All
                <span className="ml-2 text-xs bg-muted rounded-full px-2 py-0.5">
                  {notifications.length}
                </span>
              </TabsTrigger>
              <TabsTrigger value="unread">
                Unread
                <span className="ml-2 text-xs bg-muted rounded-full px-2 py-0.5">
                  {unreadCount}
                </span>
              </TabsTrigger>
              <TabsTrigger value="read">
                Read
                <span className="ml-2 text-xs bg-muted rounded-full px-2 py-0.5">
                  {notifications.length - unreadCount}
                </span>
              </TabsTrigger>
            </TabsList>

            <div className="space-y-4">
              {filteredNotifications.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <Bell className="h-12 w-12 text-muted-foreground/50 mb-4" />
                  <h3 className="text-lg font-medium">No notifications</h3>
                  <p className="text-muted-foreground mt-1">
                    {filter === 'all'
                      ? 'You don\'t have any notifications yet.'
                      : filter === 'unread'
                        ? 'You don\'t have any unread notifications.'
                        : 'You don\'t have any read notifications.'}
                  </p>
                </div>
              ) : (
                filteredNotifications.map(notification => (
                  <div
                    key={notification.id}
                    className={cn(
                      'p-4 border rounded-md flex gap-3',
                      !notification.read && 'bg-muted/30'
                    )}
                  >
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type, notification.severity)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm">{notification.message}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {format(new Date(notification.createdAt), 'MMM d, yyyy h:mm a')}
                      </p>
                    </div>
                    {!notification.read && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex-shrink-0"
                        onClick={() => handleMarkAsRead(notification.id)}
                      >
                        <Check className="h-4 w-4 mr-2" />
                        Mark as Read
                      </Button>
                    )}
                  </div>
                ))
              )}
            </div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
