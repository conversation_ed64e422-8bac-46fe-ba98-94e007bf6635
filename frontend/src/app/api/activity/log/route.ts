/**
 * Activity Logging API Route
 *
 * Processes and logs user activities to both Supabase and Neo4j for:
 * - Tenant activity tracking
 * - Proactive insights generation
 * - User behavior analysis
 *
 * Respects tenant and user isolation.
 */
import { NextResponse } from 'next/server';
import { type NextRequest } from 'next/server';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/lib/supabase/database.types';
import type { ActivityContext } from '@/lib/types';
import { withAuth } from '@/lib/auth/server-exports';
import type { AuthUser } from '@/lib/auth/types';
import { UserRole } from '@/lib/auth/types';
import { logUserActivity } from '@/lib/neo4j/client';
import OpenAI from 'openai';

// --- Environment Variable Checks ---
if (!process.env.OPENAI_API_KEY) {
  console.error('Missing OPENAI_API_KEY environment variable. LLM analysis will be disabled.');
}
if (!process.env.NEO4J_URI || !process.env.NEO4J_USER || !process.env.NEO4J_PASSWORD) {
  console.error('Neo4j connection details (URI, USER, PASSWORD) are not fully set. Activity logging will fail.');
}
// --- End Environment Variable Checks ---

const openai = process.env.OPENAI_API_KEY ? new OpenAI({ apiKey: process.env.OPENAI_API_KEY }) : null;

interface ActivityPayload {
  context: ActivityContext;
  user: AuthUser;
  timestamp: string;
}

// Define the structure we expect from the LLM
interface ActivityAnalysis {
  importance: 'high' | 'medium' | 'low';
  tags: string[];
  summary: string; // LLM generated summary
  extractedEntities?: Record<string, any>; // Optional: For future enhancement
}

/**
 * Analyzes activity context using the OpenAI Chat Completions API.
 * Aims to extract importance, relevant tags, and a concise summary.
 */
async function analyzeActivityWithLLM(context: ActivityContext): Promise<ActivityAnalysis> {
  if (!openai) {
    console.warn("[API Activity Log] OpenAI client not available. Skipping LLM analysis.");
    // Return a default/fallback structure if analysis is skipped
    return {
      summary: `Activity logged: ${context.action} on ${context.component} (Analysis skipped: OpenAI not configured)`,
      importance: 'low',
      tags: ['skipped_analysis']
    };
  }

  const systemPrompt = `
You are an intelligent legal assistant analyzing user activity logs for a personal injury law firm's case management system.
Your goal is to determine the significance of each activity, assign relevant tags, and provide a brief summary.
Analyze the provided activity context (component, action, entityType, entityId, metadata) and respond ONLY with a valid JSON object adhering to the following structure:

{
  "importance": "<high | medium | low>", // Assess urgency/importance for potential follow-up. 'high' for critical events like deadlines, new cases, client contact. 'medium' for standard updates. 'low' for routine views.
  "tags": ["<tag1>", "<tag2>", ...], // Provide relevant, concise, lowercase tags (e.g., "client_communication", "document_upload", "case_update", "deadline_management", "task_completion", "research"). Include the entityType if available.
  "summary": "<Brief, informative summary of the activity>" // Max 1-2 sentences.
}

Consider the action type and metadata. For example:
- 'DEADLINE_APPROACHING' or 'MEETING_SCHEDULED' should be 'high' importance.
- 'DOCUMENT_UPLOAD' related to evidence might be 'medium' or 'high'.
- 'CASE_VIEWED' is likely 'low' unless metadata suggests specific research.
- 'CLIENT_CONTACTED' or 'EMAIL_SENT' (to client) are 'medium' or 'high'.

Respond ONLY with the JSON object.
`;

  const userMessage = `Activity Context:
Component: ${context.component}
Action: ${context.action}
Entity Type: ${context.entityType || 'N/A'}
Entity ID: ${context.entityId || 'N/A'}
Metadata: ${JSON.stringify(context.metadata || {})}`;

  try {
    console.log(`[API Activity Log] Requesting LLM analysis for action: ${context.action}`);
    const response = await openai.chat.completions.create({
      // Consider using a model known for good JSON output, like gpt-4-turbo or gpt-3.5-turbo-1106+
      model: "gpt-3.5-turbo-1106",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userMessage },
      ],
      temperature: 0.2, // Lower temperature for more deterministic JSON output
      response_format: { type: "json_object" }, // Request JSON output
      max_tokens: 200, // Limit response size
    });

    const analysisJson = response.choices[0]?.message?.content;

    if (!analysisJson) {
      throw new Error('LLM response content is empty or missing.');
    }

    // Parse the JSON response from the LLM
    const parsedAnalysis: ActivityAnalysis = JSON.parse(analysisJson);

    // Basic validation (can be expanded)
    if (!parsedAnalysis.importance || !parsedAnalysis.tags || !parsedAnalysis.summary) {
        throw new Error('LLM response is missing required fields (importance, tags, summary).');
    }

    console.log(`[API Activity Log] LLM analysis successful for: ${context.action}`);
    return parsedAnalysis;

  } catch (error) {
    console.error(`[API Activity Log] Error during LLM analysis for action ${context.action}:`, error);
    // Return a fallback structure on error
    return {
      summary: `Activity logged: ${context.action} on ${context.component} (Analysis failed: ${error instanceof Error ? error.message : 'Unknown LLM error'})`,
      importance: 'low',
      tags: ['failed_analysis']
    };
  }
}

/**
 * API route handler for processing and logging user activities.
 * Receives buffered activities, analyzes them (potentially using LLM),
 * and stores them in Neo4j.
 * Protected by authentication.
 */
export const POST = withAuth([UserRole.Authenticated], async (request: NextRequest, user: AuthUser, supabase: SupabaseClient<Database, "public", any>, context?: Record<string, unknown>) => {
  // 'user' object (from withAuth) represents the currently logged-in user making the request
  try {
    const activities: ActivityPayload[] = await request.json();

    if (!Array.isArray(activities) || activities.length === 0) {
      return NextResponse.json({ message: 'No activities provided' }, { status: 400 });
    }

    console.log(`[API Activity Log] User ${user.id} triggering log for ${activities.length} activities.`);

    try {
      for (const activity of activities) {
        const { context, timestamp } = activity;

        // Call analysis function
        const analysisResult = await analyzeActivityWithLLM(context);
        console.log(`  - Analysis result for ${context.action}:`, analysisResult);

        // Log activity to Neo4j for tenant activity tracking and insights
        const activityId = await logUserActivity({
          userId: user.id,
          tenantId: user.tenantId || '',
          activityType: context.action,
          llmSummary: analysisResult.summary,
          importance: analysisResult.importance,
          tags: analysisResult.tags,
          caseId: context.entityType === 'case' ? context.entityId : undefined,
          documentId: context.entityType === 'document' ? context.entityId : undefined,
          contactId: context.entityType === 'contact' ? context.entityId : undefined,
          metadata: {
             ...context.metadata,
             component: context.component,
             clientTimestamp: timestamp,
             analysis: analysisResult,
             tenant_id: user.tenantId // Ensure tenant isolation
          }
        });

        // Also log to Supabase for backup and immediate querying
        const { logActivity } = await import('@/lib/activity-logger');
        await logActivity({
          userId: user.id,
          type: context.action,
          description: analysisResult.summary,
          metadata: {
            ...context.metadata,
            component: context.component,
            importance: analysisResult.importance,
            tags: analysisResult.tags,
            neo4j_activity_id: activityId
          },
          caseId: context.entityType === 'case' ? context.entityId : undefined,
          documentId: context.entityType === 'document' ? context.entityId : undefined,
          clientId: context.entityType === 'client' ? context.entityId : undefined
        });
        console.log(`  - Logged activity: ${context.action} for user ${user.id}`);
      }

      console.log(`[API Activity Log] Processed ${activities.length} activities for user ${user.id}.`);
      return NextResponse.json({ message: `Successfully processed ${activities.length} activities` }, { status: 200 });

    } catch (innerError) {
      console.error('[API Activity Log] Error during activity processing loop:', innerError);
      return NextResponse.json({ message: 'Error processing one or more activities' }, { status: 500 });
    }

  } catch (error) {
    console.error('[API Activity Log] Error processing activities:', error);
    if (error instanceof Response) {
        return error;
    }
    return NextResponse.json({ message: 'Internal Server Error processing activities' }, { status: 500 });
  }
});
