'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Save, User, Building, FileText, Gavel, Briefcase, Calculator, Calendar } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { useToast } from '@/components/ui/use-toast'
import { useMattersApi } from '@/hooks/useMattersApi'
import { PracticeArea, WorkType, MatterStatus, MatterPriorityLevel } from '@/types/domain/tenants/Matter'
import { PRACTICE_AREA_GROUPS, getPracticeAreaOption, getWorkTypeFromPracticeArea, getSuggestedWorkType } from '@/config/practice-areas'

// Form data interface
interface NewMatterFormData {
  title: string
  description: string
  practiceArea: PracticeArea | ''
  workType: WorkType | ''
  clientId: string
  status: MatterStatus
  sensitive: boolean
  priorityLevel: MatterPriorityLevel
  trialDate: string
  metadata: Record<string, unknown>
}

export default function NewMatterPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { createMatter } = useMattersApi()

  // Form state
  const [formData, setFormData] = useState<NewMatterFormData>({
    title: '',
    description: '',
    practiceArea: '',
    workType: '',
    clientId: '',
    status: MatterStatus.PENDING,
    sensitive: false,
    priorityLevel: MatterPriorityLevel.MEDIUM,
    trialDate: '',
    metadata: {}
  })

  const [loading, setLoading] = useState(false)
  const [clients, setClients] = useState<Array<{ id: string; fullName: string }>>([])

  // Load clients for selection
  useEffect(() => {
    // TODO: Implement client fetching
    // For now, using mock data
    setClients([
      { id: '1', fullName: 'John Doe' },
      { id: '2', fullName: 'Jane Smith' }
    ])
  }, [])

  // Handle form field changes
  const handleInputChange = (field: keyof NewMatterFormData, value: string | boolean): void => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Handle practice area change (automatically sets suggested work type)
  const handlePracticeAreaChange = (practiceArea: PracticeArea): void => {
    const suggestedWorkType = getSuggestedWorkType(practiceArea)
    setFormData(prev => ({
      ...prev,
      practiceArea,
      workType: suggestedWorkType
    }))
  }

  // Handle work type change
  const handleWorkTypeChange = (workType: WorkType): void => {
    setFormData(prev => ({
      ...prev,
      workType
    }))
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation
    if (!formData.title.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Matter title is required',
        variant: 'destructive'
      })
      return
    }

    if (!formData.practiceArea) {
      toast({
        title: 'Validation Error',
        description: 'Practice area is required',
        variant: 'destructive'
      })
      return
    }

    if (!formData.workType) {
      toast({
        title: 'Validation Error',
        description: 'Work type is required',
        variant: 'destructive'
      })
      return
    }

    if (!formData.clientId) {
      toast({
        title: 'Validation Error',
        description: 'Client selection is required',
        variant: 'destructive'
      })
      return
    }

    setLoading(true)
    
    try {
      // Prepare matter data
      const matterData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        practiceArea: formData.practiceArea,
        workType: formData.workType,
        clientId: formData.clientId,
        status: formData.status,
        sensitive: formData.sensitive,
        priorityLevel: formData.priorityLevel,
        trialDate: formData.trialDate || null,
        metadata: formData.metadata
      }

      const newMatter = await createMatter(matterData)
      
      if (!newMatter || typeof newMatter !== 'object' || !('id' in newMatter)) {
        throw new Error('Invalid response from server')
      }
      
      toast({
        title: 'Success',
        description: 'Matter created successfully',
      })

      // Navigate to the new matter
      router.push(`/matters/${(newMatter as { id: string }).id}`)
      
    } catch (error) {
      console.error('Error creating matter:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create matter',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  // Handle cancel
  const handleCancel = (): void => {
    router.push('/matters')
  }

  // Get matter type label based on work type
  const getMatterTypeLabel = () => {
    if (!formData.workType) return 'Matter'
    return formData.workType === WorkType.LITIGATION ? 'Case' : 'Matter'
  }

  return (
    <div className="container max-w-4xl mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button variant="outline" size="icon" onClick={handleCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create New {getMatterTypeLabel()}</h1>
          <p className="text-muted-foreground mt-1">
            {formData.practiceArea
              ? `Add a new ${getPracticeAreaOption(formData.practiceArea)?.label.toLowerCase()} ${getMatterTypeLabel().toLowerCase()} to your practice`
              : `Add a new ${getMatterTypeLabel().toLowerCase()} to your practice`
            }
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Matter Title *</Label>
              <Input
                id="title"
                placeholder="Enter matter title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Brief description of the matter"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="practiceArea">Practice Area *</Label>
                <Select
                  value={formData.practiceArea}
                  onValueChange={(value) => handlePracticeAreaChange(value as PracticeArea)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select practice area" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[400px]">
                    {Object.values(PracticeArea).map((practiceArea) => {
                      const option = getPracticeAreaOption(practiceArea)
                      return (
                        <SelectItem key={practiceArea} value={practiceArea}>
                          <div className="flex flex-col gap-1 py-1">
                            <div className="font-medium">{option?.label || practiceArea}</div>
                            <div className="text-xs text-muted-foreground">{option?.description}</div>
                          </div>
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="workType">Work Type *</Label>
                <Select
                  value={formData.workType}
                  onValueChange={(value) => handleWorkTypeChange(value as WorkType)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select work type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={WorkType.LITIGATION}>
                      <div className="flex flex-col gap-1 py-1">
                        <div className="font-medium">Litigation</div>
                        <div className="text-xs text-muted-foreground">Court proceedings, disputes, and trials</div>
                      </div>
                    </SelectItem>
                    <SelectItem value={WorkType.TRANSACTIONAL}>
                      <div className="flex flex-col gap-1 py-1">
                        <div className="font-medium">Transactional</div>
                        <div className="text-xs text-muted-foreground">Business transactions and commercial deals</div>
                      </div>
                    </SelectItem>
                    <SelectItem value={WorkType.ADVISORY}>
                      <div className="flex flex-col gap-1 py-1">
                        <div className="font-medium">Advisory</div>
                        <div className="text-xs text-muted-foreground">Legal counsel and strategic advice</div>
                      </div>
                    </SelectItem>
                    <SelectItem value={WorkType.ADR}>
                      <div className="flex flex-col gap-1 py-1">
                        <div className="font-medium">Alternative Dispute Resolution</div>
                        <div className="text-xs text-muted-foreground">Mediation, arbitration, and settlement</div>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                {formData.practiceArea && formData.workType && (
                  <div className="mt-2 p-2 bg-muted/30 rounded-md">
                    <div className="text-xs font-medium text-muted-foreground">Selected:</div>
                    <div className="text-sm font-medium">{getPracticeAreaOption(formData.practiceArea)?.label} - {getMatterTypeLabel()}</div>
                  </div>
                )}
              </div>

            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Initial Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={MatterStatus.PENDING}>Pending</SelectItem>
                  <SelectItem value={MatterStatus.ACTIVE}>Active</SelectItem>
                  <SelectItem value={MatterStatus.ON_HOLD}>On Hold</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Client Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Client Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="client">Client *</Label>
              <Select 
                value={formData.clientId} 
                onValueChange={(value) => handleInputChange('clientId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a client" />
                </SelectTrigger>
                <SelectContent>
                  {clients.map((client) => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.fullName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Matter Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5" />
              {getMatterTypeLabel()} Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="priority">Priority Level</Label>
                <Select 
                  value={formData.priorityLevel} 
                  onValueChange={(value) => handleInputChange('priorityLevel', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={MatterPriorityLevel.LOW}>Low</SelectItem>
                    <SelectItem value={MatterPriorityLevel.MEDIUM}>Medium</SelectItem>
                    <SelectItem value={MatterPriorityLevel.HIGH}>High</SelectItem>
                    <SelectItem value={MatterPriorityLevel.URGENT}>Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="trialDate">
                  {formData.workType === WorkType.LITIGATION ? 'Trial Date' : 'Key Deadline'}
                </Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="trialDate"
                    type="date"
                    value={formData.trialDate}
                    onChange={(e) => handleInputChange('trialDate', e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="sensitive"
                checked={formData.sensitive}
                onCheckedChange={(checked) => handleInputChange('sensitive', checked)}
              />
              <Label htmlFor="sensitive">Mark as sensitive/confidential</Label>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Create {getMatterTypeLabel()}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}