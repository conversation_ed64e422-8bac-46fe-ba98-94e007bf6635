'use client';

import * as React from 'react';
import { CopilotChat } from '@copilotkit/react-ui';
import { useCoAgent } from '@copilotkit/react-core';

// Define the state interface
interface EventAgentState {
  event_title: string;
  event_description: string;
  start_time: string; // ISO string
  end_time: string; // ISO string
  all_day: boolean;
  location: string;
  event_type: string;
  case_id?: string;
  client_id?: string;
  assigned_to: string[];
  status: string;
  progress_percentage: number;
}

export function EventAgentChat(): React.ReactElement {
  const [isOpen, setIsOpen] = React.useState(false);

  // Connect to the event_agent
  const { state, setState } = useCoAgent<EventAgentState>({
    name: "event_agent"
  });

  // Template handlers
  const applyTemplate = (template: string) => {
    if (template === 'client-meeting') {
      setState(prevState => ({
        ...prevState,
        event_title: "Client Meeting",
        event_type: "meeting",
        event_description: "Meeting with client to discuss case details and next steps.",
      } as EventAgentState));
    } else if (template === 'court-filing') {
      setState(prevState => ({
        ...prevState,
        event_title: "Court Filing",
        event_type: "filing",
        event_description: "Deadline for filing court documents.",
      } as EventAgentState));
    } else if (template === 'deposition') {
      setState(prevState => ({
        ...prevState,
        event_title: "Deposition",
        event_type: "deposition",
        event_description: "Witness deposition for ongoing case.",
      } as EventAgentState));
    } else if (template === 'team-briefing') {
      setState(prevState => ({
        ...prevState,
        event_title: "Team Briefing",
        event_type: "meeting",
        event_description: "Internal team meeting to discuss case strategy.",
      } as EventAgentState));
    }
  };

  // Event-related prompts for suggestions
  const eventPrompts = [
    "Extract details for a client meeting next Tuesday at 2pm",
    "Schedule a deposition with Dr. Smith at Memorial Hospital",
    "Create an event for the court hearing on May 15th",
    "Add a settlement conference with opposing counsel",
    "Schedule a team briefing about the Johnson case",
  ];

  return (
    <div className="space-y-6">
      <div className="rounded-lg bg-slate-50 p-4">
        <h3 className="font-semibold mb-2">Event AI Assistant</h3>
        <p className="text-sm text-muted-foreground mb-4">
          I can help you extract and organize event details from natural language descriptions.
        </p>

        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full bg-blue-600 text-white p-2 rounded mb-4 hover:bg-blue-700"
        >
          {isOpen ? "Close AI Assistant" : "Open AI Assistant"}
        </button>

        {isOpen && (
          <div className="border rounded-lg h-[400px] bg-white">
            <CopilotChat
              className="w-full h-full"
              suggestions={eventPrompts}
              context={{
                currentView: "event_creation",
                agentName: "event_agent"
              }}
              initialMessage="Hello! I'm your Event Assistant. Please describe the event you'd like to schedule, including when, where, and who will be involved. I'll help extract all the details."
            />
          </div>
        )}

        {/* Display extracted information */}
        {state && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h4 className="font-medium mb-2">Extracted Details</h4>
            <div className="space-y-1 text-sm">
              {state.event_title && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Title:</span>
                  <span className="font-medium">{state.event_title}</span>
                </div>
              )}
              {state.event_type && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Type:</span>
                  <span className="font-medium">{state.event_type}</span>
                </div>
              )}
              {state.start_time && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Date/Time:</span>
                  <span className="font-medium">{new Date(state.start_time).toLocaleString()}</span>
                </div>
              )}
              {state.location && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Location:</span>
                  <span className="font-medium">{state.location}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      <div className="rounded-lg border p-4">
        <h3 className="font-semibold mb-2">Quick Templates</h3>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={() => applyTemplate('client-meeting')}
            className="w-full bg-gray-200 p-1 rounded text-sm hover:bg-gray-300"
          >
            Client Meeting
          </button>
          <button
            onClick={() => applyTemplate('court-filing')}
            className="w-full bg-gray-200 p-1 rounded text-sm hover:bg-gray-300"
          >
            Court Filing
          </button>
          <button
            onClick={() => applyTemplate('deposition')}
            className="w-full bg-gray-200 p-1 rounded text-sm hover:bg-gray-300"
          >
            Deposition
          </button>
          <button
            onClick={() => applyTemplate('team-briefing')}
            className="w-full bg-gray-200 p-1 rounded text-sm hover:bg-gray-300"
          >
            Team Briefing
          </button>
        </div>
      </div>

      <div className="rounded-lg border p-4">
        <h3 className="font-semibold mb-2">Risk Assessment</h3>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">Scheduling Conflicts</span>
            <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">Low</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm">Deadline Proximity</span>
            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded">Medium</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm">Resource Availability</span>
            <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">High</span>
          </div>
        </div>
      </div>
    </div>
  );
}
