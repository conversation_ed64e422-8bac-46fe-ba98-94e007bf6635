/**
 * Compliance Services Unit Tests
 *
 * Comprehensive unit tests for compliance audit systems,
 * regulatory framework validation, and compliance reporting.
 */

// Mock environment variables before any imports using hoisted
vi.hoisted(() => {
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://anwefmklplkjxkmzpnva.supabase.co';
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-key';
  process.env.SENTRY_DSN = '';
  process.env.NODE_ENV = 'test';
});

// Mock Sentry to prevent module resolution issues
vi.mock('@sentry/nextjs', () => ({
  withSentryConfig: vi.fn((config) => config),
  captureException: vi.fn(),
  captureMessage: vi.fn(),
  withSentry: vi.fn((handler) => handler),
}));

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ComplianceAuditService } from '@/lib/services/compliance-audit-service';
import { ComplianceFrameworkValidator } from '@/lib/services/compliance-framework-validator';

// Mock dependencies
vi.mock('@supabase/supabase-js');

const mockSupabase = {
  from: vi.fn().mockReturnValue({
    select: vi.fn().mockReturnValue({
      eq: vi.fn().mockReturnValue({
        order: vi.fn().mockReturnValue({
          limit: vi.fn().mockResolvedValue({ data: [], _error: null })
        })
      })
    }),
    insert: vi.fn().mockReturnValue({
      select: vi.fn().mockReturnValue({
        single: vi.fn().mockResolvedValue({ data: { id: 'test-id' }, error: null })
      })
    })
  }),
  schema: vi.fn().mockReturnThis()
};

vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn().mockReturnValue(mockSupabase)
}));

describe('Compliance Services', () => {
  let auditService: ComplianceAuditService;
  let frameworkValidator: ComplianceFrameworkValidator;

  beforeEach(() => {
    vi.clearAllMocks();
    auditService = new ComplianceAuditService();
    frameworkValidator = new ComplianceFrameworkValidator();
  });

  describe('Compliance Audit Service', () => {
    describe('PCI DSS Compliance', () => {
      it('should validate PCI DSS Level 1 requirements', async () => {
        const paymentData = {
          encryption_at_rest: true,
          encryption_in_transit: true,
          access_controls: true,
          audit_logging: true,
          network_security: true,
          vulnerability_management: true,
          secure_development: true,
          information_security_policy: true,
          incident_response_plan: true,
          penetration_testing: true
        };

        const result = await auditService.validatePCIDSS(paymentData);
        
        expect(result.compliant).toBe(true);
        expect(result.level).toBe('Level 1');
        expect(result.violations).toHaveLength(0);
        expect(result.score).toBeGreaterThanOrEqual(95);
      });

      it('should identify PCI DSS violations', async () => {
        const paymentData = {
          encryption_at_rest: false, // Violation
          encryption_in_transit: true,
          access_controls: false, // Violation
          audit_logging: true,
          network_security: true,
          vulnerability_management: false, // Violation
          secure_development: true,
          information_security_policy: true,
          incident_response_plan: true,
          penetration_testing: true
        };

        const result = await auditService.validatePCIDSS(paymentData);
        
        expect(result.compliant).toBe(false);
        expect(result.violations.length).toBeGreaterThan(0);
        expect(result.violations).toContainEqual(
          expect.objectContaining({
            requirement: 'encryption_at_rest',
            severity: 'critical'
          })
        );
      });

      it('should calculate compliance scores correctly', async () => {
        const partiallyCompliant = {
          encryption_at_rest: true,
          encryption_in_transit: true,
          access_controls: true,
          audit_logging: true,
          network_security: false,
          vulnerability_management: false,
          secure_development: true,
          information_security_policy: true,
          incident_response_plan: false,
          penetration_testing: false
        };

        const result = await auditService.validatePCIDSS(partiallyCompliant);
        
        expect(result.score).toBeGreaterThan(0);
        expect(result.score).toBeLessThan(100);
        expect(result.compliant).toBe(false);
      });
    });

    describe('GDPR Compliance', () => {
      it('should validate GDPR data protection requirements', async () => {
        const dataProtection = {
          lawful_basis: 'consent',
          data_minimization: true,
          purpose_limitation: true,
          accuracy: true,
          storage_limitation: true,
          integrity_confidentiality: true,
          accountability: true,
          privacy_by_design: true,
          data_subject_rights: true,
          breach_notification: true,
          dpo_appointed: true,
          privacy_impact_assessment: true
        };

        const result = await auditService.validateGDPR(dataProtection);
        
        expect(result.compliant).toBe(true);
        expect(result.violations).toHaveLength(0);
        expect(result.dataSubjectRights).toBeDefined();
        expect(result.dataSubjectRights.length).toBeGreaterThan(0);
      });

      it('should identify GDPR violations', async () => {
        const dataProtection = {
          lawful_basis: null, // Violation
          data_minimization: false, // Violation
          purpose_limitation: true,
          accuracy: true,
          storage_limitation: false, // Violation
          integrity_confidentiality: true,
          accountability: true,
          privacy_by_design: true,
          data_subject_rights: false, // Violation
          breach_notification: true,
          dpo_appointed: false, // Violation
          privacy_impact_assessment: true
        };

        const result = await auditService.validateGDPR(dataProtection);
        
        expect(result.compliant).toBe(false);
        expect(result.violations.length).toBeGreaterThan(0);
        expect(result.violations).toContainEqual(
          expect.objectContaining({
            article: 'Article 6',
            requirement: 'lawful_basis'
          })
        );
      });

      it('should validate data subject rights implementation', async () => {
        const rightsImplementation = {
          right_to_information: true,
          right_of_access: true,
          right_to_rectification: true,
          right_to_erasure: true,
          right_to_restrict_processing: true,
          right_to_data_portability: true,
          right_to_object: true,
          rights_related_to_automated_decision_making: true
        };

        const result = await auditService.validateDataSubjectRights(rightsImplementation);
        
        expect(result.compliant).toBe(true);
        expect(result.implementedRights).toHaveLength(8);
        expect(result.missingRights).toHaveLength(0);
      });
    });

    describe('SOX Compliance', () => {
      it('should validate SOX financial controls', async () => {
        const financialControls = {
          internal_controls: true,
          financial_reporting_accuracy: true,
          management_assessment: true,
          auditor_attestation: true,
          disclosure_controls: true,
          ceo_cfo_certification: true,
          whistleblower_protection: true,
          audit_committee_independence: true,
          real_time_disclosure: true,
          code_of_ethics: true
        };

        const result = await auditService.validateSOX(financialControls);
        
        expect(result.compliant).toBe(true);
        expect(result.section302Compliant).toBe(true);
        expect(result.section404Compliant).toBe(true);
        expect(result.violations).toHaveLength(0);
      });

      it('should identify SOX control deficiencies', async () => {
        const financialControls = {
          internal_controls: false, // Material weakness
          financial_reporting_accuracy: true,
          management_assessment: false, // Violation
          auditor_attestation: true,
          disclosure_controls: false, // Violation
          ceo_cfo_certification: true,
          whistleblower_protection: true,
          audit_committee_independence: false, // Violation
          real_time_disclosure: true,
          code_of_ethics: true
        };

        const result = await auditService.validateSOX(financialControls);
        
        expect(result.compliant).toBe(false);
        expect(result.materialWeaknesses.length).toBeGreaterThan(0);
        expect(result.significantDeficiencies.length).toBeGreaterThan(0);
      });
    });

    describe('Regional Compliance', () => {
      it('should validate SEPA compliance for EU payments', async () => {
        const sepaCompliance = {
          sepa_creditor_identifier: 'DE98ZZZ09999999999',
          mandate_management: true,
          pre_notification: true,
          return_processing: true,
          dispute_resolution: true,
          data_protection: true,
          customer_authentication: true,
          transaction_monitoring: true
        };

        const result = await auditService.validateSEPA(sepaCompliance);
        
        expect(result.compliant).toBe(true);
        expect(result.creditorIdentifierValid).toBe(true);
        expect(result.violations).toHaveLength(0);
      });

      it('should validate PSD2 compliance for EU payments', async () => {
        const psd2Compliance = {
          strong_customer_authentication: true,
          open_banking_apis: true,
          payment_service_provider_license: true,
          operational_resilience: true,
          incident_reporting: true,
          outsourcing_arrangements: true,
          governance_arrangements: true
        };

        const result = await auditService.validatePSD2(psd2Compliance);
        
        expect(result.compliant).toBe(true);
        expect(result.scaCompliant).toBe(true);
        expect(result.violations).toHaveLength(0);
      });
    });
  });

  describe('Compliance Framework Validator', () => {
    describe('Framework Selection', () => {
      it('should select appropriate frameworks for US operations', () => {
        const frameworks = frameworkValidator.getApplicableFrameworks('US', ['payment_processing']);
        
        expect(frameworks).toContain('PCI_DSS');
        expect(frameworks).toContain('SOX');
        expect(frameworks).toContain('CCPA');
        expect(frameworks).not.toContain('GDPR');
        expect(frameworks).not.toContain('PSD2');
      });

      it('should select appropriate frameworks for EU operations', () => {
        const frameworks = frameworkValidator.getApplicableFrameworks('DE', ['payment_processing']);
        
        expect(frameworks).toContain('PCI_DSS');
        expect(frameworks).toContain('GDPR');
        expect(frameworks).toContain('PSD2');
        expect(frameworks).toContain('SEPA');
        expect(frameworks).not.toContain('SOX');
        expect(frameworks).not.toContain('CCPA');
      });

      it('should handle multi-jurisdiction operations', () => {
        const frameworks = frameworkValidator.getApplicableFrameworks(['US', 'DE'], ['payment_processing']);
        
        expect(frameworks).toContain('PCI_DSS'); // Global requirement
        expect(frameworks).toContain('GDPR'); // EU requirement
        expect(frameworks).toContain('SOX'); // US requirement
        expect(frameworks).toContain('PSD2'); // EU requirement
      });
    });

    describe('Risk Assessment', () => {
      it('should assess compliance risk levels', () => {
        const riskFactors = {
          transaction_volume: 'high',
          data_sensitivity: 'high',
          geographic_scope: 'multi_region',
          industry_sector: 'financial_services',
          regulatory_history: 'clean'
        };

        const assessment = frameworkValidator.assessComplianceRisk(riskFactors);
        
        expect(assessment.overallRisk).toBe('high');
        expect(assessment.priorityFrameworks).toContain('PCI_DSS');
        expect(assessment.recommendedActions.length).toBeGreaterThan(0);
      });

      it('should provide risk mitigation recommendations', () => {
        const riskFactors = {
          transaction_volume: 'medium',
          data_sensitivity: 'high',
          geographic_scope: 'single_region',
          industry_sector: 'legal_services',
          regulatory_history: 'minor_violations'
        };

        const assessment = frameworkValidator.assessComplianceRisk(riskFactors);
        
        expect(assessment.mitigationStrategies).toBeDefined();
        expect(assessment.mitigationStrategies.length).toBeGreaterThan(0);
        expect(assessment.timelineRecommendations).toBeDefined();
      });
    });

    describe('Compliance Monitoring', () => {
      it('should generate compliance monitoring schedules', () => {
        const frameworks = ['PCI_DSS', 'GDPR', 'SOX'];
        const schedule = frameworkValidator.generateMonitoringSchedule(frameworks);
        
        expect(schedule.daily).toBeDefined();
        expect(schedule.weekly).toBeDefined();
        expect(schedule.monthly).toBeDefined();
        expect(schedule.quarterly).toBeDefined();
        expect(schedule.annually).toBeDefined();
        
        // PCI DSS requires quarterly vulnerability scans
        expect(schedule.quarterly).toContainEqual(
          expect.objectContaining({
            framework: 'PCI_DSS',
            activity: 'vulnerability_scan'
          })
        );
      });

      it('should track compliance metrics', async () => {
        const metrics = await frameworkValidator.getComplianceMetrics('tenant-123');
        
        expect(metrics).toBeDefined();
        expect(metrics.overallScore).toBeGreaterThanOrEqual(0);
        expect(metrics.overallScore).toBeLessThanOrEqual(100);
        expect(metrics.frameworkScores).toBeDefined();
        expect(metrics.trendData).toBeDefined();
      });
    });

    describe('Audit Trail', () => {
      it('should log compliance events', async () => {
        const event = {
          framework: 'PCI_DSS',
          event_type: 'vulnerability_scan',
          result: 'passed',
          details: { scan_id: 'scan-123', vulnerabilities_found: 0 }
        };

        const result = await frameworkValidator.logComplianceEvent('tenant-123', event);
        
        expect(result.success).toBe(true);
        expect(result.eventId).toBeDefined();
        
        // Verify database call
        expect(mockSupabase.from).toHaveBeenCalledWith('compliance_events');
      });

      it('should generate compliance reports', async () => {
        const report = await frameworkValidator.generateComplianceReport('tenant-123', {
          frameworks: ['PCI_DSS', 'GDPR'],
          period: 'quarterly',
          includeRecommendations: true
        });
        
        expect(report).toBeDefined();
        expect(report.summary).toBeDefined();
        expect(report.frameworkDetails).toBeDefined();
        expect(report.recommendations).toBeDefined();
        expect(report.actionItems.length).toBeGreaterThanOrEqual(0);
      });
    });
  });
});
