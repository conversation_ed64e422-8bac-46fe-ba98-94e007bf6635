import type { Metadata } from 'next';
import { createClient } from '@/lib/auth/server-exports';
import { UsageDashboard } from '@/components/subscription/usage-dashboard';
import { Database } from '@/lib/supabase/database.types';

export const metadata: Metadata = {
  title: 'Usage Dashboard',
  description: 'Track your resource usage and quota limits',
};

export default async function UsagePage() {
  const supabase = await createClient();

  // Get the current user
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return (
      <div className="container mx-auto py-10">
        <h1 className="text-2xl font-bold mb-4">Usage Dashboard</h1>
        <p>Please log in to view your usage information.</p>
      </div>
    );
  }

  // Get the tenant ID from the user's metadata
  const tenantId = user.app_metadata.tenant_id;

  if (!tenantId) {
    return (
      <div className="container mx-auto py-10">
        <h1 className="text-2xl font-bold mb-4">Usage Dashboard</h1>
        <p>No tenant ID found for your account. Please contact support.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-4">Usage Dashboard</h1>
      <p className="text-gray-500 mb-6">
        Track your resource usage and quota limits. Monitor your document uploads, processing, API calls, and storage usage.
      </p>

      <UsageDashboard tenantId={tenantId} />
    </div>
  );
}
