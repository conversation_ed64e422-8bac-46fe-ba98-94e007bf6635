'use client'

import { useState, useEffect } from 'react'
import { useR<PERSON><PERSON>, useSearchParams } from 'next/navigation'
import {
  Search,
  FileText,
  Filter,
  ArrowLeft,
  Download,
  Clock,
  CheckCircle,
  AlertCircle,
  Link as LinkIcon,
  Calendar
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useSupabase } from '@/lib/supabase/provider'
import { useToast } from '@/components/ui/use-toast'
import { Badge } from '@/components/ui/badge'
import { Pagination, PaginationContent, PaginationItem, PaginationLink } from '@/components/ui/pagination'

// Document type options - matches the DOCUMENT_TYPES in document-upload.tsx
const DOCUMENT_TYPES = [
  { id: "all", label: "All Types" },
  { id: "medical", label: "Medical Records" },
  { id: "insurance", label: "Insurance Documents" },
  { id: "police", label: "Police Reports" },
  { id: "legal", label: "Legal Documents" },
  { id: "photos", label: "Photos & Evidence" },
  { id: "correspondence", label: "Correspondence" },
  { id: "other", label: "Other Documents" }
];

// Sample data - would be replaced with actual data from Supabase
const mockDocuments = [
  {
    id: 'doc1',
    title: 'Medical Records - Johnson',
    type: 'medical',
    size: 2458000, // bytes
    uploadedAt: '2025-04-01T10:30:00Z',
    caseId: 'case1',
    caseName: 'Johnson v. City Hospital',
    clientName: 'Sarah Johnson',
    processingStatus: 'completed',
    url: '#'
  },
  {
    id: 'doc2',
    title: 'Insurance Claim Form',
    type: 'insurance',
    size: 542000, // bytes
    uploadedAt: '2025-04-03T14:45:00Z',
    caseId: 'case2',
    caseName: 'Williams Auto Accident',
    clientName: 'Robert Williams',
    processingStatus: 'completed',
    url: '#'
  },
  {
    id: 'doc3',
    title: 'Police Report - Smith Accident',
    type: 'police',
    size: 1240000, // bytes
    uploadedAt: '2025-04-05T09:15:00Z',
    caseId: 'case3',
    caseName: 'Smith v. Jones',
    clientName: 'John Smith',
    processingStatus: 'processing',
    url: '#'
  },
  {
    id: 'doc4',
    title: 'Settlement Offer Letter',
    type: 'legal',
    size: 320000, // bytes
    uploadedAt: '2025-04-06T11:20:00Z',
    caseId: 'case1',
    caseName: 'Johnson v. City Hospital',
    clientName: 'Sarah Johnson',
    processingStatus: 'completed',
    url: '#'
  },
  {
    id: 'doc5',
    title: 'Accident Scene Photos',
    type: 'photos',
    size: 8540000, // bytes
    uploadedAt: '2025-04-02T16:10:00Z',
    caseId: 'case3',
    caseName: 'Smith v. Jones',
    clientName: 'John Smith',
    processingStatus: 'error',
    url: '#'
  }
];

export default function DocumentLibraryPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { supabase } = useSupabase()
  const { toast } = useToast()

  // States
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '')
  const [documentType, setDocumentType] = useState('all')
  const [caseFilter, setCaseFilter] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [documents, setDocuments] = useState<any[]>([])
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [loading, setLoading] = useState(true)

  // Cases for filtering - would normally come from API
  const [cases, setCases] = useState<any[]>([
    { id: 'all', name: 'All Cases' },
    { id: 'case1', name: 'Johnson v. City Hospital' },
    { id: 'case2', name: 'Williams Auto Accident' },
    { id: 'case3', name: 'Smith v. Jones' }
  ])

  // Fetch documents
  useEffect(() => {
    const fetchDocuments = async () => {
      setLoading(true)
      try {
        // In a real implementation, this would make an API call
        // const response = await fetch(
        //   `/api/documents?page=${page}&type=${documentType}&case=${caseFilter}&status=${statusFilter}&search=${searchQuery}`
        // );
        // const data = await response.json();

        // For now, we'll filter the mock data
        let filteredDocs = [...mockDocuments];

        // Apply search filter
        if (searchQuery) {
          filteredDocs = filteredDocs.filter(doc =>
            doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            doc.clientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
            doc.caseName.toLowerCase().includes(searchQuery.toLowerCase())
          )
        }

        // Apply document type filter
        if (documentType !== 'all') {
          filteredDocs = filteredDocs.filter(doc => doc.type === documentType)
        }

        // Apply case filter
        if (caseFilter && caseFilter !== 'all') {
          filteredDocs = filteredDocs.filter(doc => doc.caseId === caseFilter)
        }

        // Apply status filter
        if (statusFilter !== 'all') {
          filteredDocs = filteredDocs.filter(doc => doc.processingStatus === statusFilter)
        }

        setDocuments(filteredDocs)
        setTotalPages(Math.ceil(filteredDocs.length / 10)) // 10 items per page
      } catch (error) {
        console.error('Error fetching documents:', error)
        toast({
          title: 'Error',
          description: 'Failed to load documents',
          variant: 'destructive'
        })
      } finally {
        setLoading(false)
      }
    }

    fetchDocuments()
  }, [searchQuery, documentType, caseFilter, statusFilter, page, toast])

  // Handle search
  const handleSearch = (): void => {
    setPage(1) // Reset to first page when searching
  }

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
  }

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch(status) {
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1"><CheckCircle className="h-3 w-3" /> Processed</Badge>
      case 'processing':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 flex items-center gap-1"><Clock className="h-3 w-3" /> Processing</Badge>
      case 'error':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 flex items-center gap-1"><AlertCircle className="h-3 w-3" /> Failed</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  return (
    <div className="container max-w-7xl mx-auto py-8 px-4">
      {/* Breadcrumb */}
      <div className="mb-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/document-center">Document Center</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/document-center/library">Library</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Document Library</h1>
          <p className="text-muted-foreground mt-1">
            Browse and search all your case documents
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button onClick={() => router.push('/document-center/upload')}>
            Upload Documents
          </Button>
        </div>
      </div>

      {/* Search and filters */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center">
            <Filter className="h-4 w-4 mr-2" />
            Search and Filter
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="md:col-span-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search documents..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>

            <div>
              <Select value={documentType} onValueChange={setDocumentType}>
                <SelectTrigger>
                  <SelectValue placeholder="Document Type" />
                </SelectTrigger>
                <SelectContent>
                  {DOCUMENT_TYPES.map(type => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Select value={caseFilter} onValueChange={setCaseFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Case" />
                </SelectTrigger>
                <SelectContent>
                  {cases.map(c => (
                    <SelectItem key={c.id} value={c.id}>
                      {c.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
            <div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Processing Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="completed">Processed</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="error">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="md:col-span-3 flex justify-end">
              <Button variant="outline" onClick={handleSearch}>
                <Search className="mr-2 h-4 w-4" />
                Apply Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents Table */}
      <Card>
        <CardContent className="pt-6">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Document</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Case</TableHead>
                <TableHead>Upload Date</TableHead>
                <TableHead>Size</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Clock className="h-5 w-5 mr-2 animate-spin" />
                      Loading documents...
                    </div>
                  </TableCell>
                </TableRow>
              ) : documents.length > 0 ? (
                documents.map((doc) => (
                  <TableRow
                    key={doc.id}
                    className="cursor-pointer"
                    onClick={() => router.push(`/document-center/library/${doc.id}`)}
                  >
                    <TableCell>
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="font-medium">{doc.title}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="capitalize">{doc.type}</span>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        {doc.caseName}
                      </div>
                    </TableCell>
                    <TableCell>{formatDate(doc.uploadedAt)}</TableCell>
                    <TableCell>{formatFileSize(doc.size)}</TableCell>
                    <TableCell>{renderStatusBadge(doc.processingStatus)}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
                            <span className="sr-only">Open menu</span>
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                            </svg>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent onClick={(e) => e.stopPropagation()} align="end">
                          <DropdownMenuItem onClick={() => router.push(`/document-center/library/${doc.id}`)}>
                            <FileText className="mr-2 h-4 w-4" />
                            View Document
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => window.open(doc.url, '_blank')}>
                            <Download className="mr-2 h-4 w-4" />
                            Download
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => router.push(`/cases/${doc.caseId}`)}>
                            <LinkIcon className="mr-2 h-4 w-4" />
                            View Related Case
                          </DropdownMenuItem>
                          {doc.processingStatus === 'error' && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => console.log('Retry processing for', doc.id)}>
                                <Clock className="mr-2 h-4 w-4" />
                                Retry Processing
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <p>No documents found matching your filters.</p>
                    <Button
                      variant="link"
                      className="mt-2"
                      onClick={() => {
                        setSearchQuery('')
                        setDocumentType('all')
                        setCaseFilter('')
                        setStatusFilter('all')
                      }}
                    >
                      Clear all filters
                    </Button>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>

        {/* Pagination */}
        {totalPages > 1 && (
          <CardFooter className="flex justify-center py-4">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationLink
                    href="#"
                    onClick={(e) => { e.preventDefault(); if (page !== 1) setPage(1); }}
                    aria-disabled={page === 1}
                    className={page === 1 ? 'pointer-events-none opacity-50' : ''}
                  >
                    First
                  </PaginationLink>
                </PaginationItem>

                <PaginationItem>
                  <PaginationLink
                    href="#"
                    onClick={(e) => { e.preventDefault(); if (page !== 1) setPage(prev => Math.max(prev - 1, 1)); }}
                    aria-disabled={page === 1}
                    className={page === 1 ? 'pointer-events-none opacity-50' : ''}
                  >
                    Previous
                  </PaginationLink>
                </PaginationItem>

                <PaginationItem>
                  <span className="px-4 py-2">
                    Page {page} of {totalPages}
                  </span>
                </PaginationItem>

                <PaginationItem>
                  <PaginationLink
                    href="#"
                    onClick={(e) => { e.preventDefault(); if (page !== totalPages) setPage(prev => Math.min(prev + 1, totalPages)); }}
                    aria-disabled={page === totalPages}
                    className={page === totalPages ? 'pointer-events-none opacity-50' : ''}
                  >
                    Next
                  </PaginationLink>
                </PaginationItem>

                <PaginationItem>
                  <PaginationLink
                    href="#"
                    onClick={(e) => { e.preventDefault(); if (page !== totalPages) setPage(totalPages); }}
                    aria-disabled={page === totalPages}
                    className={page === totalPages ? 'pointer-events-none opacity-50' : ''}
                  >
                    Last
                  </PaginationLink>
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </CardFooter>
        )}
      </Card>
    </div>
  )
}
