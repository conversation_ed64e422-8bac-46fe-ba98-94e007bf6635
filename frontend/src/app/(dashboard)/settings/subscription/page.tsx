/**
 * Subscription Management Page
 * 
 * Comprehensive subscription management interface for law firm customers
 * with plan details, usage tracking, and subscription controls.
 */

'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Settings, 
  Calendar, 
  Users, 
  BarChart3, 
  CreditCard, 
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Clock,
  Shield,
  Zap
} from 'lucide-react';

interface SubscriptionData {
  id: string;
  plan: {
    id: string;
    name: string;
    price: number;
    currency: string;
    features: string[];
  };
  status: 'active' | 'trialing' | 'past_due' | 'canceled' | 'unpaid';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  trialEnd?: string;
  cancelAtPeriodEnd: boolean;
  usage: {
    users: { current: number; limit: number };
    cases: { current: number; limit: number };
    storage: { current: number; limit: number };
    aiRequests: { current: number; limit: number };
  };
}

export default function SubscriptionManagementPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  
  // Mock subscription data - in real app, fetch from API
  const subscription: SubscriptionData = {
    id: 'sub_123',
    plan: {
      id: 'team',
      name: 'Team Plan',
      price: 199,
      currency: 'USD',
      features: [
        'Multi-user collaboration',
        'Advanced case management',
        'AI-powered legal research',
        'Document automation',
        'Team analytics',
        'Priority support'
      ]
    },
    status: 'active',
    currentPeriodStart: '2024-01-15',
    currentPeriodEnd: '2024-02-15',
    trialEnd: undefined,
    cancelAtPeriodEnd: false,
    usage: {
      users: { current: 3, limit: 10 },
      cases: { current: 45, limit: 100 },
      storage: { current: 2.3, limit: 10 },
      aiRequests: { current: 1250, limit: 5000 }
    }
  };
  
  const getStatusBadge = (status: string) => {
    const variants = {
      active: { variant: 'default' as const, label: 'Active', color: 'text-green-600' },
      trialing: { variant: 'secondary' as const, label: 'Trial', color: 'text-blue-600' },
      past_due: { variant: 'destructive' as const, label: 'Past Due', color: 'text-red-600' },
      canceled: { variant: 'outline' as const, label: 'Canceled', color: 'text-gray-600' },
      unpaid: { variant: 'destructive' as const, label: 'Unpaid', color: 'text-red-600' }
    };
    
    const config = variants[status as keyof typeof variants] || variants.active;
    return (
      <Badge variant={config.variant} className={config.color}>
        {config.label}
      </Badge>
    );
  };
  
  const getUsagePercentage = (current: number, limit: number) => {
    return Math.round((current / limit) * 100);
  };
  
  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };
  
  const handleUpgradePlan = (): void => {
    router.push('/subscribe?upgrade=true');
  };
  
  const handleCancelSubscription = (): void => {
    // In real app, show confirmation modal and handle cancellation
    console.log('Cancel subscription');
  };
  
  const handleReactivateSubscription = (): void => {
    // In real app, handle reactivation
    console.log('Reactivate subscription');
  };
  
  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Subscription Management
        </h1>
        <p className="text-gray-600">
          Manage your subscription, monitor usage, and control your account settings
        </p>
      </div>
      
      {/* Status Alert */}
      {subscription.status === 'trialing' && subscription.trialEnd && (
        <Alert>
          <Clock className="h-4 w-4" />
          <AlertDescription>
            Your trial ends on {new Date(subscription.trialEnd).toLocaleDateString()}. 
            Upgrade now to continue using all features.
          </AlertDescription>
        </Alert>
      )}
      
      {subscription.status === 'past_due' && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Your subscription is past due. Please update your payment method to avoid service interruption.
          </AlertDescription>
        </Alert>
      )}
      
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="usage">Usage & Limits</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>
        
        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Current Plan */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Current Plan
                </div>
                {getStatusBadge(subscription.status)}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold">{subscription.plan.name}</h3>
                  <p className="text-gray-600">
                    ${subscription.plan.price}/{subscription.plan.currency.toLowerCase()} per month
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Next billing date</p>
                  <p className="font-medium">{new Date(subscription.currentPeriodEnd).toLocaleDateString()}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">Plan Features</h4>
                  <ul className="space-y-1 text-sm">
                    {subscription.plan.features.slice(0, 3).map((feature, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        {feature}
                      </li>
                    ))}
                    {subscription.plan.features.length > 3 && (
                      <li className="text-gray-500">
                        +{subscription.plan.features.length - 3} more features
                      </li>
                    )}
                  </ul>
                </div>
                
                <div className="space-y-2">
                  <Button onClick={handleUpgradePlan} className="w-full">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Upgrade Plan
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => router.push('/settings/billing')}
                    className="w-full"
                  >
                    <CreditCard className="h-4 w-4 mr-2" />
                    Manage Billing
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Quick Usage Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium">
                    {subscription.usage.users.current}/{subscription.usage.users.limit}
                  </span>
                </div>
                <h3 className="font-medium">Team Members</h3>
                <Progress 
                  value={getUsagePercentage(subscription.usage.users.current, subscription.usage.users.limit)} 
                  className="mt-2"
                />
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-2">
                  <BarChart3 className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium">
                    {subscription.usage.cases.current}/{subscription.usage.cases.limit}
                  </span>
                </div>
                <h3 className="font-medium">Active Cases</h3>
                <Progress 
                  value={getUsagePercentage(subscription.usage.cases.current, subscription.usage.cases.limit)} 
                  className="mt-2"
                />
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-2">
                  <Shield className="h-5 w-5 text-purple-600" />
                  <span className="text-sm font-medium">
                    {subscription.usage.storage.current}GB/{subscription.usage.storage.limit}GB
                  </span>
                </div>
                <h3 className="font-medium">Storage Used</h3>
                <Progress 
                  value={getUsagePercentage(subscription.usage.storage.current, subscription.usage.storage.limit)} 
                  className="mt-2"
                />
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-2">
                  <Zap className="h-5 w-5 text-yellow-600" />
                  <span className="text-sm font-medium">
                    {subscription.usage.aiRequests.current}/{subscription.usage.aiRequests.limit}
                  </span>
                </div>
                <h3 className="font-medium">AI Requests</h3>
                <Progress 
                  value={getUsagePercentage(subscription.usage.aiRequests.current, subscription.usage.aiRequests.limit)} 
                  className="mt-2"
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Usage Tab */}
        <TabsContent value="usage" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Usage Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {Object.entries(subscription.usage).map(([key, usage]) => {
                const percentage = getUsagePercentage(usage.current, usage.limit);
                const isNearLimit = percentage >= 80;
                
                return (
                  <div key={key} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1')}</h3>
                      <span className={`text-sm font-medium ${isNearLimit ? 'text-red-600' : 'text-gray-600'}`}>
                        {typeof usage.current === 'number' && usage.current % 1 !== 0 
                          ? `${usage.current.toFixed(1)}${key === 'storage' ? 'GB' : ''}` 
                          : usage.current
                        } / {usage.limit}{key === 'storage' ? 'GB' : ''}
                      </span>
                    </div>
                    <Progress 
                      value={percentage} 
                      className={`h-2 ${getUsageColor(percentage)}`}
                    />
                    {isNearLimit && (
                      <p className="text-sm text-red-600">
                        You're approaching your {key} limit. Consider upgrading your plan.
                      </p>
                    )}
                  </div>
                );
              })}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Billing Tab */}
        <TabsContent value="billing">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Billing Management
                </h3>
                <p className="text-gray-600 mb-4">
                  Manage your payment methods, view invoices, and update billing information.
                </p>
                <Button onClick={() => router.push('/settings/billing')}>
                  Go to Billing Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Subscription Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-medium">Auto-renewal</h3>
                  <p className="text-sm text-gray-600">
                    Automatically renew your subscription each billing period
                  </p>
                </div>
                <Badge variant={subscription.cancelAtPeriodEnd ? 'destructive' : 'default'}>
                  {subscription.cancelAtPeriodEnd ? 'Disabled' : 'Enabled'}
                </Badge>
              </div>
              
              <div className="space-y-2">
                {subscription.cancelAtPeriodEnd ? (
                  <Button onClick={handleReactivateSubscription} className="w-full">
                    Reactivate Subscription
                  </Button>
                ) : (
                  <Button 
                    variant="destructive" 
                    onClick={handleCancelSubscription}
                    className="w-full"
                  >
                    Cancel Subscription
                  </Button>
                )}
                
                <p className="text-xs text-gray-500 text-center">
                  {subscription.cancelAtPeriodEnd 
                    ? 'Your subscription will end on ' + new Date(subscription.currentPeriodEnd).toLocaleDateString()
                    : 'You can cancel anytime. Your subscription will remain active until the end of your billing period.'
                  }
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
