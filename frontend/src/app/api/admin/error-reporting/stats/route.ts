import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const hoursBack = parseInt(searchParams.get('hours_back') || '24');

    // Get error statistics
    const { data: statsData, error: statsError } = await supabase
      .rpc('error_reporting.get_error_stats' as any, { p_hours_back: hoursBack });

    if (statsError) {
      console.error('Error fetching error stats:', statsError);
      return NextResponse.json(
        { error: 'Failed to fetch error statistics' },
        { status: 500 }
      );
    }

    const stats = statsData && statsData.length > 0 ? statsData[0] : {
      total_errors: 0,
      critical_errors: 0,
      high_errors: 0,
      medium_errors: 0,
      low_errors: 0,
      unique_errors: 0,
      affected_users: 0,
      error_rate_per_hour: 0
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error in error-reporting stats API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
