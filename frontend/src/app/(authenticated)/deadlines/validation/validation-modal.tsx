'use client'

import React, {  useState  } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Calendar, Clock, FileText } from 'lucide-react'
import type { Deadline } from '@/types/deadlines';

interface ValidationModalProps {
  isOpen: boolean
  onClose: () => void
  deadline: Deadline | null
  onValidate: (note: string) => void
  onReject: (note: string) => void
}

export function DeadlineValidationModal({
  isOpen,
  onClose,
  deadline,
  onValidate,
  onReject
}: ValidationModalProps): React.ReactElement | null {
  const [note, setNote] = useState('')

  if (!deadline) return null

  const formatDate = (dateString: string) => {
    if (!dateString) return 'No date'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Validate Deadline</DialogTitle>
          <DialogDescription>
            Review and validate this automatically extracted deadline
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="mb-4 space-y-2">
            <h3 className="font-medium">{deadline.description}</h3>

            <div className="flex items-center text-sm text-muted-foreground">
              <Calendar className="h-4 w-4 mr-1" />
              <span>{formatDate(deadline.due_date)}</span> {/* Updated field name */}

              <Clock className="h-4 w-4 ml-3 mr-1" />
              <span>Priority: {deadline.priority || 'Medium'}</span>
            </div>

            {deadline.document_id && (
              <div className="flex items-center text-sm text-muted-foreground">
                <FileText className="h-4 w-4 mr-1" />
                <span>Source: {deadline.documents?.title || 'Document ' + deadline.document_id.substring(0, 8)}</span>
              </div>
            )}
          </div>

          <div className="space-y-2 mb-4">
            <p className="text-sm font-medium">Details:</p>
            <div className="text-sm space-y-1 pl-2 border-l-2 border-muted">
              <p><span className="text-muted-foreground">Jurisdiction:</span> {deadline.jurisdiction}</p>
              {deadline.legal_basis && (
                <p><span className="text-muted-foreground">Legal Basis:</span> {deadline.legal_basis}</p>
              )}
              {deadline.consequences && (
                <p><span className="text-muted-foreground">Consequences:</span> {deadline.consequences}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="validation-note" className="text-sm font-medium">
              Add a validation note (optional):
            </label>
            <Textarea
              id="validation-note"
              value={note}
              onChange={(e) => setNote(e.target.value)}
              placeholder="Add context or corrections..."
              className="w-full"
              rows={3}
            />
          </div>
        </div>

        <DialogFooter className="flex justify-end space-x-2">
          <Button variant="outline" onClick={() => onReject(note)}>
            Reject
          </Button>
          <Button onClick={() => onValidate(note)}>
            Validate
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
