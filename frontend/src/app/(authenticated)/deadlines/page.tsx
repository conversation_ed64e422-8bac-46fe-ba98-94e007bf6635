'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Calendar, Clock, FileText, Filter } from 'lucide-react'
import { CalendarDeadlineWithRelations } from '@/lib/services/calendar-deadline-service';
import { createServices } from '@/lib/services';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/lib/supabase/database.types';
import type { TypedSupabaseClient } from '@/lib/supabase/client-types';
import { useUser } from '@/contexts/UserContext';

export default function DeadlinesPage() {
  const [deadlines, setDeadlines] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [filters, setFilters] = useState({
    priority: '',
    status: '',
    search: ''
  })
  const { user, tenantId } = useUser();
  // Create Supabase client using the browser environment variables
  const supabase = createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  ) as any as TypedSupabaseClient;

  useEffect(() => {
    if (user && tenantId) {
      fetchDeadlines()
    }
  }, [user, tenantId])

  async function fetchDeadlines() {
    setIsLoading(true)

    try {
      if (!tenantId) {
        throw new Error('Tenant ID is required to fetch deadlines');
      }

      // Use our service layer with proper tenant isolation
      const services = createServices(supabase, tenantId);

      // Use our deadlines service to fetch data
      const result = await services.deadlines.getAll();
      // Convert received deadlines to the format expected by the UI
      const formattedDeadlines = (result.deadlines || []).map((deadline: any) => ({
        ...deadline,
        // Add UI-friendly properties that match what the component expects
        dueDate: deadline.due_date,
        caseId: deadline.case_id,
        validationStatus: deadline.validation_status || 'pending',
        validationNote: deadline.validation_note || '',
        // Providing defaults for potentially missing fields
        jurisdiction: 'Unknown',  // Default value since field might not exist
        legalBasis: '',          // Default value since field might not exist
        documentId: null,         // Default value since field might not exist
        // Add empty objects to prevent null reference errors
        document: { title: '' },  // Default object
        case: { title: '' }       // Default object
      }));

      setDeadlines(formattedDeadlines)
    } catch (error) {
      console.error('Error fetching deadlines:', error)
    } finally {
      setIsLoading(false)
    }
  }

  function getPriorityVariant(priority: string | null | undefined): "default" | "destructive" | "outline" | "secondary" {
    if (!priority) return 'secondary';
    switch (priority.toLowerCase()) {
      case 'critical': return 'destructive'
      case 'high': return 'default'
      case 'medium': return 'secondary'
      case 'low': return 'outline'
      default: return 'secondary'
    }
  }

  // Adjusted return type to match Badge variants
  function getStatusVariant(status: string | null | undefined): "default" | "destructive" | "outline" | "secondary" {
    if (!status) return 'secondary';
    switch (status.toLowerCase()) {
      case 'validated': return 'default'
      case 'rejected': return 'destructive'
      case 'pending': return 'secondary'
      default: return 'secondary'
    }
  }

  function formatDate(dateString: string | undefined): string {
    if (!dateString) return 'No date'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Filter deadlines based on user-selected filters
  const filteredDeadlines = deadlines.filter((deadline: any) => {
    if (filters.priority && deadline.priority?.toLowerCase() !== filters.priority.toLowerCase()) {
      return false
    }

    // Updated field name to camelCase
    if (filters.status && deadline.validationStatus?.toLowerCase() !== filters.status.toLowerCase()) {
      return false
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase()
      return (
        deadline.description?.toLowerCase().includes(searchTerm) ||
        deadline.jurisdiction?.toLowerCase().includes(searchTerm) ||
        deadline.legalBasis?.toLowerCase().includes(searchTerm)  // Updated field name
      )
    }

    return true
  })

  return (
    <div>
      {/* Filters */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center">
            <Filter className="h-4 w-4 mr-2" />
            Filter Deadlines
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search deadlines..."
                value={filters.search}
                onChange={(e) => setFilters({...filters, search: e.target.value})}
              />
            </div>
            <div className="w-full md:w-48">
              <Select
                value={filters.priority}
                onValueChange={(value) => setFilters({...filters, priority: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Priorities</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="w-full md:w-48">
              <Select
                value={filters.status}
                onValueChange={(value) => setFilters({...filters, status: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="validated">Validated</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button
              variant="outline"
              onClick={() => {
                setFilters({
                  priority: '',
                  status: '',
                  search: ''
                })
              }}
            >
              Reset
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Deadlines List */}
      {isLoading ? (
        <div className="flex justify-center py-12">
          <p>Loading deadlines...</p>
        </div>
      ) : filteredDeadlines.length === 0 ? (
        <Card>
          <CardContent className="py-12">
            <p className="text-center text-muted-foreground">No deadlines found</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredDeadlines.map((deadline) => {
            // Use camelCase validationStatus from our domain model
            let borderColor = 'border-l-amber-400'
            if (deadline.validationStatus === 'validated') {
              borderColor = 'border-l-green-500'
            } else if (deadline.validationStatus === 'rejected') {
              borderColor = 'border-l-red-500'
            }

            return (
              <Card key={deadline.id} className={`border-l-4 ${borderColor} hover:shadow-md transition-shadow`}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{deadline.description}</CardTitle>
                      <CardDescription className="flex items-center mt-1">
                        <Calendar className="h-4 w-4 mr-1" />
                        {formatDate(deadline.dueDate)} {/* Using camelCase */}
                      </CardDescription>
                    </div>
                    <div className="flex flex-col gap-2 items-end">
                      <Badge variant={getPriorityVariant(deadline.priority)}>
                        {deadline.priority ?? 'Medium'}
                      </Badge>
                      {deadline.validationStatus && (
                        <Badge variant={getStatusVariant(deadline.validationStatus)}>
                          {deadline.validationStatus}
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pb-2">
                  <div className="text-sm grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div>
                      <p><strong>Jurisdiction:</strong> {deadline.jurisdiction}</p>
                      {deadline.legalBasis && (
                        <p><strong>Legal Basis:</strong> {deadline.legalBasis}</p>
                      )}
                    </div>
                    <div>
                      {deadline.documentId && (
                        <p className="flex items-center text-muted-foreground">
                          <FileText className="h-3 w-3 mr-1" />
                          {deadline.document?.title || 'Document ' + deadline.documentId.substring(0, 8)}
                        </p>
                      )}
                      {deadline.caseId && (
                        <p className="text-muted-foreground">
                          <strong>Case:</strong> {deadline.case?.title || deadline.caseId.substring(0, 8)}
                        </p>
                      )}
                      {deadline.validationNote && (
                        <p className="text-muted-foreground mt-1">
                          <strong>Note:</strong> {deadline.validationNote}
                        </p>
                      )}
                    </div>
                  </div>
                </CardContent>

                <CardFooter className="flex justify-end space-x-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                  >
                    View Details
                  </Button>
                </CardFooter>
              </Card>
            )
          })}
        </div>
      )}
    </div>
  )
}
