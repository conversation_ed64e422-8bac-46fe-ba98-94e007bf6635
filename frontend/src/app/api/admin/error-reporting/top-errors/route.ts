import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const hoursBack = parseInt(searchParams.get('hours_back') || '24');

    // Get top errors
    const { data: topErrorsData, error: topErrorsError } = await supabase
      .rpc('error_reporting.get_top_errors' as any, {
        p_limit: limit,
        p_hours_back: hoursBack
      });

    if (topErrorsError) {
      console.error('Error fetching top errors:', topErrorsError);
      return NextResponse.json(
        { error: 'Failed to fetch top errors' },
        { status: 500 }
      );
    }

    return NextResponse.json(topErrorsData || []);
  } catch (error) {
    console.error('Error in top-errors API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
