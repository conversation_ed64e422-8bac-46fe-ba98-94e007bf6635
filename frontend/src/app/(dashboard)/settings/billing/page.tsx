/**
 * Billing Settings Page
 * 
 * Professional billing management page for law firm customers
 * with payment method management and subscription controls.
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CreditCard, 
  Building2, 
  Calendar, 
  DollarSign, 
  Settings, 
  Plus,
  Shield,
  Download,
  AlertCircle
} from 'lucide-react';
import { PaymentMethodManager } from '@/components/payment/PaymentMethodManager';
import type { StripePaymentMethodCreationResponse } from '@/lib/types/payment-methods';

interface PaymentMethod {
  id: string;
  type: 'card' | 'ach' | 'sepa_debit';
  last4: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
}

interface Subscription {
  id: string;
  plan: string;
  status: 'active' | 'past_due' | 'canceled';
  currentPeriodEnd: string;
  amount: number;
  currency: string;
}

export default function BillingSettingsPage() {
  const [showAddPaymentMethod, setShowAddPaymentMethod] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    {
      id: 'pm_1',
      type: 'card',
      last4: '4242',
      brand: 'visa',
      expiryMonth: 12,
      expiryYear: 2025,
      isDefault: true
    },
    {
      id: 'pm_2',
      type: 'ach',
      last4: '6789',
      isDefault: false
    }
  ]);
  
  // Mock data - in real app, fetch from API
  const subscription: Subscription = {
    id: 'sub_123',
    plan: 'Team Plan',
    status: 'active',
    currentPeriodEnd: '2024-02-15',
    amount: 199,
    currency: 'USD'
  };
  
  const user = {
    tenantId: 'tenant-123',
    country: 'US' as const,
    currency: 'USD' as const
  };
  
  const handlePaymentMethodAdded = (response: StripePaymentMethodCreationResponse): void => {
    // In real app, refresh payment methods from API
    console.log('Payment method added:', response);
    setShowAddPaymentMethod(false);
    
    // Mock adding to list
    const newMethod: PaymentMethod = {
      id: response.stripe_payment_method_id,
      type: 'card', // Would come from response
      last4: '1234', // Would come from response
      brand: 'mastercard',
      isDefault: false
    };
    
    setPaymentMethods(prev => [...prev, newMethod]);
  };
  
  const handleSetDefault = (methodId: string): void => {
    setPaymentMethods(prev => 
      prev.map(method => ({
        ...method,
        isDefault: method.id === methodId
      }))
    );
  };
  
  const handleRemoveMethod = (methodId: string): void => {
    setPaymentMethods(prev => prev.filter(method => method.id !== methodId));
  };
  
  const getPaymentMethodIcon = (type: string) => {
    switch (type) {
      case 'card':
        return <CreditCard className="h-5 w-5" />;
      case 'ach':
      case 'sepa_debit':
        return <Building2 className="h-5 w-5" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };
  
  const getPaymentMethodDisplay = (method: PaymentMethod) => {
    if (method.type === 'card') {
      return `${method.brand?.toUpperCase()} •••• ${method.last4}`;
    }
    return `Bank Account •••• ${method.last4}`;
  };
  
  if (showAddPaymentMethod) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="mb-6">
          <Button 
            variant="ghost" 
            onClick={() => setShowAddPaymentMethod(false)}
            className="mb-4"
          >
            ← Back to Billing Settings
          </Button>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Add Payment Method
          </h1>
          <p className="text-gray-600">
            Add a new payment method to your account
          </p>
        </div>
        
        <PaymentMethodManager
          tenantId={user.tenantId}
          countryCode={user.country}
          currencyCode={user.currency}
          amountCents={subscription.amount * 100}
          onPaymentMethodCreated={handlePaymentMethodAdded}
          onError={(error) => console.error('Payment method error:', error)}
          className="max-w-2xl"
        />
      </div>
    );
  }
  
  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Billing & Subscription
        </h1>
        <p className="text-gray-600">
          Manage your subscription, payment methods, and billing information
        </p>
      </div>
      
      {/* Current Subscription */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Current Subscription
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-lg">{subscription.plan}</h3>
              <p className="text-gray-600">
                ${subscription.amount}/{subscription.currency.toLowerCase()} per month
              </p>
            </div>
            <Badge 
              variant={subscription.status === 'active' ? 'default' : 'destructive'}
              className="capitalize"
            >
              {subscription.status}
            </Badge>
          </div>
          
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>Renews on {subscription.currentPeriodEnd}</span>
            </div>
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              <span>Next charge: ${subscription.amount}</span>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              Change Plan
            </Button>
            <Button variant="outline" size="sm">
              Cancel Subscription
            </Button>
          </div>
        </CardContent>
      </Card>
      
      {/* Payment Methods */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Methods
            </CardTitle>
            <Button 
              onClick={() => setShowAddPaymentMethod(true)}
              size="sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Method
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {paymentMethods.map((method) => (
            <div 
              key={method.id}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div className="flex items-center gap-3">
                {getPaymentMethodIcon(method.type)}
                <div>
                  <p className="font-medium">
                    {getPaymentMethodDisplay(method)}
                  </p>
                  {method.expiryMonth && method.expiryYear && (
                    <p className="text-sm text-gray-600">
                      Expires {method.expiryMonth}/{method.expiryYear}
                    </p>
                  )}
                  {method.isDefault && (
                    <Badge variant="secondary" className="mt-1">
                      Default
                    </Badge>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {!method.isDefault && (
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleSetDefault(method.id)}
                  >
                    Set Default
                  </Button>
                )}
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => handleRemoveMethod(method.id)}
                  disabled={method.isDefault}
                >
                  Remove
                </Button>
              </div>
            </div>
          ))}
          
          {paymentMethods.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No payment methods added yet</p>
              <Button 
                onClick={() => setShowAddPaymentMethod(true)}
                className="mt-4"
              >
                Add Your First Payment Method
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Billing History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Billing History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              { date: '2024-01-15', amount: 199, status: 'paid', invoice: 'INV-001' },
              { date: '2023-12-15', amount: 199, status: 'paid', invoice: 'INV-002' },
              { date: '2023-11-15', amount: 199, status: 'paid', invoice: 'INV-003' }
            ].map((invoice, index) => (
              <div key={index} className="flex items-center justify-between py-3 border-b last:border-b-0">
                <div>
                  <p className="font-medium">{invoice.invoice}</p>
                  <p className="text-sm text-gray-600">{invoice.date}</p>
                </div>
                <div className="flex items-center gap-4">
                  <span className="font-medium">${invoice.amount}</span>
                  <Badge variant="secondary">Paid</Badge>
                  <Button variant="ghost" size="sm">
                    Download
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Security Notice */}
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Your payment information is encrypted and secure. We use industry-standard 
          security measures and never store your full payment details.
        </AlertDescription>
      </Alert>
      
      {/* Support */}
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <h3 className="font-medium text-gray-900 mb-2">
              Need help with billing?
            </h3>
            <p className="text-gray-600 mb-4">
              Our support team is here to help with any billing questions.
            </p>
            <Button variant="outline">
              Contact Support
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
