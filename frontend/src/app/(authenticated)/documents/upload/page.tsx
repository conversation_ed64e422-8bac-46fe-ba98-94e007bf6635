"use client";

import React, { useState } from "react";
import type { UploadedFile } from "@/components/documents/document-upload";
import { DocumentUpload } from "@/components/documents/document-upload";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { AlertCircle, ArrowLeft, CheckCircle, FileText } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";

export default function DocumentUploadPage() {
  const { toast } = useToast();
  const router = useRouter();
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [uploadComplete, setUploadComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleUploadComplete = (files: UploadedFile[]): void => {
    setUploadedFiles((prev) => [...prev, ...files]);
    setUploadComplete(true);

    toast({
      title: "Upload Complete",
      description: `Successfully uploaded ${files.length} document${files.length > 1 ? 's' : ''}.`,
    });
  };

  const handleGoToDocuments = (): void => {
    router.push("/documents");
  };

  const handleNewUpload = (): void => {
    setUploadedFiles([]);
    setUploadComplete(false);
    setError(null);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center">
        <Button variant="ghost" className="mr-2" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Document Upload</h1>
      </div>

      <Separator />

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {uploadComplete && uploadedFiles.length > 0 ? (
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <CardTitle>Upload Complete</CardTitle>
            </div>
            <CardDescription>
              Your documents have been successfully uploaded and are ready for processing.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Uploaded Documents</h3>
                <ul className="space-y-1">
                  {uploadedFiles.map((file) => (
                    <li key={file.id} className="flex items-center text-sm">
                      <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span className="truncate">{file.name}</span>
                      <span className="ml-auto text-xs text-muted-foreground">
                        {file.documentType}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex space-x-2 pt-4">
                <Button onClick={handleNewUpload}>Upload More Documents</Button>
                <Button variant="outline" onClick={handleGoToDocuments}>
                  Go to Documents
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Upload Case Documents</CardTitle>
            <CardDescription>
              Upload documents related to your case. Accepted formats include PDF,
              images (JPG, PNG), and text documents (DOC, DOCX, TXT).
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DocumentUpload
              onUploadComplete={handleUploadComplete}
              maxFiles={10}
              maxSize={50}
            />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
