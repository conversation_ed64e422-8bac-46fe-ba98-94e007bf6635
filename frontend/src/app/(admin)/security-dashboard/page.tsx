'use client'

import React, { useEffect, useState } from 'react'
import { format } from 'date-fns'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase/client'
import { useSupabase } from '@/lib/supabase/provider'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { DataTable } from '@/components/ui/data-table'
import type { ColumnDef } from '@tanstack/react-table'
import { ExclamationTriangleIcon, ShieldCheckIcon, ClockIcon } from '@heroicons/react/24/outline'
import { RoleBasedComponent } from '@/components/auth/RoleBasedComponent'
import { useRbac } from '@/hooks/useRbac'

interface SecurityEvent {
  id: string
  event_type: string
  user_id: string
  user_email: string
  ip_address: string
  user_agent: string
  details: any
  created_at: string
}

export default function SecurityDashboard() {
  const { supabase } = useSupabase()
  const router = useRouter()
  const rbac = useRbac()
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([])
  const [realtimeEnabled, setRealtimeEnabled] = useState(false)
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalEvents: 0,
    suspiciousActivities: 0,
    loginAttempts: 0,
    profileChanges: 0
  })

  // SECURITY: Access control is handled by RoleBasedComponent wrapper at the bottom
  // This provides defense-in-depth security that cannot be bypassed

  // Show loading state while checking permissions
  if (rbac.isLoading()) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  useEffect(() => {
    // Fetch initial security events
    fetchSecurityEvents()

    // Set up realtime subscription
    const channel = supabase
      .channel('security-events')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'security',
        table: 'events'
      }, (payload: { new: SecurityEvent }) => {
        setSecurityEvents(prev => [payload.new, ...prev])
        updateStats([payload.new, ...securityEvents])
      })
      .subscribe((status: string) => {
        setRealtimeEnabled(status === 'SUBSCRIBED')
      })

    return () => {
      supabase.removeChannel(channel)
    }
  }, [])

  async function fetchSecurityEvents() {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('security.dashboard_events')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(100)

      if (error) throw error

      if (data) {
        setSecurityEvents(data)
        updateStats(data)
      }
    } catch (error) {
      console.error('Error fetching security events:', error)
    } finally {
      setLoading(false)
    }
  }

  function updateStats(events: SecurityEvent[]) {
    setStats({
      totalEvents: events.length,
      suspiciousActivities: events.filter(e => e.event_type.includes('suspicious')).length,
      loginAttempts: events.filter(e => e.event_type.includes('login')).length,
      profileChanges: events.filter(e => e.event_type.includes('profile')).length
    })
  }

  const columns: ColumnDef<SecurityEvent>[] = [
    {
      accessorKey: 'event_type',
      header: 'Event Type',
      cell: ({ row }) => {
        const eventType = row.getValue('event_type') as string
        let badgeVariant = 'default'

        if (eventType.includes('suspicious')) badgeVariant = 'destructive'
        else if (eventType.includes('login')) badgeVariant = 'secondary'
        else if (eventType.includes('profile')) badgeVariant = 'outline'

        return <Badge variant={badgeVariant as any}>{eventType}</Badge>
      }
    },
    {
      accessorKey: 'user_email',
      header: 'User',
      cell: ({ row }) => row.getValue('user_email') || 'Anonymous'
    },
    {
      accessorKey: 'ip_address',
      header: 'IP Address'
    },
    {
      accessorKey: 'created_at',
      header: 'Timestamp',
      cell: ({ row }) => {
        const timestamp = row.getValue('created_at') as string
        return format(new Date(timestamp), 'MMM dd, yyyy HH:mm:ss')
      }
    },
    {
      id: 'actions',
      cell: ({ row }) => {
        const event = row.original
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              alert(JSON.stringify(event.details, null, 2))
            }}
          >
            Details
          </Button>
        )
      }
    }
  ]

  return (
    <RoleBasedComponent
      allowedRoles={['partner']}
      showFallback={true}
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <Card className="w-96">
            <CardHeader>
              <CardTitle className="text-red-600">Access Denied</CardTitle>
            </CardHeader>
            <CardContent>
              <p>You don't have permission to access the security dashboard.</p>
              <p className="text-sm text-gray-600 mt-2">Only administrators can view security information.</p>
              <Button
                onClick={() => router.push('/dashboard')}
                className="mt-4"
              >
                Return to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      }
    >
      <div className="container mx-auto py-10">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Security Dashboard</h1>
          <div className="flex items-center gap-2">
            <Badge variant={realtimeEnabled ? 'default' : 'outline'}>
              {realtimeEnabled ? 'Realtime Active' : 'Realtime Inactive'}
            </Badge>
            <Button onClick={fetchSecurityEvents} variant="outline" size="sm">
              Refresh
            </Button>
          </div>
        </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Events</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <ClockIcon className="h-4 w-4 mr-2 text-muted-foreground" />
              <p className="text-2xl font-bold">{stats.totalEvents}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Suspicious Activities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <ExclamationTriangleIcon className="h-4 w-4 mr-2 text-destructive" />
              <p className="text-2xl font-bold">{stats.suspiciousActivities}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Login Attempts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <ShieldCheckIcon className="h-4 w-4 mr-2 text-muted-foreground" />
              <p className="text-2xl font-bold">{stats.loginAttempts}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Profile Changes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <ClockIcon className="h-4 w-4 mr-2 text-muted-foreground" />
              <p className="text-2xl font-bold">{stats.profileChanges}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList>
          <TabsTrigger value="all">All Events</TabsTrigger>
          <TabsTrigger value="suspicious">Suspicious</TabsTrigger>
          <TabsTrigger value="logins">Logins</TabsTrigger>
          <TabsTrigger value="profiles">Profile Changes</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          <DataTable
            columns={columns}
            data={securityEvents}
            loading={loading}
          />
        </TabsContent>

        <TabsContent value="suspicious" className="mt-6">
          <DataTable
            columns={columns}
            data={securityEvents.filter(e => e.event_type.includes('suspicious'))}
            loading={loading}
          />
        </TabsContent>

        <TabsContent value="logins" className="mt-6">
          <DataTable
            columns={columns}
            data={securityEvents.filter(e => e.event_type.includes('login'))}
            loading={loading}
          />
        </TabsContent>

        <TabsContent value="profiles" className="mt-6">
          <DataTable
            columns={columns}
            data={securityEvents.filter(e => e.event_type.includes('profile'))}
            loading={loading}
          />
        </TabsContent>
      </Tabs>

      {securityEvents.length === 0 && !loading && (
        <div className="flex flex-col items-center justify-center py-12">
          <ShieldCheckIcon className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-xl font-medium mb-2">No security events found</h3>
          <p className="text-muted-foreground">
            Security events will appear here as they occur
          </p>
        </div>
      )}
      </div>
    </RoleBasedComponent>
  )
}
