import type { ReactNode } from 'react';
import { redirect } from 'next/navigation';
import { createClient } from '@/lib/auth/server-exports';
import { UserRole } from '@/lib/types/auth';

export default async function SettingsLayout({ children }: { children: ReactNode }) {
  // Server-side role validation for settings access
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();

  // Check if user is authenticated
  if (!session?.user) {
    redirect('/login?_error=authentication_required&redirect=/settings');
  }

  // Extract user role from session metadata
  const userRole = session.user.app_metadata?.role || session.user.user_metadata?.role;

  // Only admin users can access settings
  if (userRole !== UserRole.Admin) {
    redirect('/dashboard?error=admin_required&message=Settings access requires admin privileges');
  }

  return (
    <div className="min-h-screen bg-background">
      {children}
    </div>
  );
}
