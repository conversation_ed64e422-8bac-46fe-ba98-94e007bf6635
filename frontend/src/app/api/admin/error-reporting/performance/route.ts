import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const hoursBack = parseInt(searchParams.get('hours_back') || '24');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Get performance metrics
    const { data: perfData, error: perfError } = await supabase
      .from('error_reporting.performance_metrics')
      .select('endpoint, duration_ms, metric_type, timestamp')
      .gte('timestamp', new Date(Date.now() - hoursBack * 60 * 60 * 1000).toISOString())
      .not('endpoint', 'is', null)
      .order('timestamp', { ascending: false });

    if (perfError) {
      console.error('Error fetching performance metrics:', perfError);
      return NextResponse.json(
        { error: 'Failed to fetch performance metrics' },
        { status: 500 }
      );
    }

    // Aggregate performance data by endpoint
    const aggregated = (perfData || []).reduce((acc: any, metric: any) => {
      const endpoint = metric.endpoint;
      if (!acc[endpoint]) {
        acc[endpoint] = {
          endpoint,
          durations: [],
          request_count: 0,
          metric_type: metric.metric_type
        };
      }
      acc[endpoint].durations.push(metric.duration_ms);
      acc[endpoint].request_count++;
      return acc;
    }, {});

    // Calculate statistics for each endpoint
    const perfMetrics = Object.values(aggregated).map((item: any) => ({
      endpoint: item.endpoint,
      metric_type: item.metric_type,
      avg_duration: Math.round(item.durations.reduce((a: number, b: number) => a + b, 0) / item.durations.length),
      max_duration: Math.max(...item.durations),
      min_duration: Math.min(...item.durations),
      p95_duration: Math.round(item.durations.sort((a: number, b: number) => a - b)[Math.floor(item.durations.length * 0.95)]),
      request_count: item.request_count
    })).sort((a: any, b: any) => b.avg_duration - a.avg_duration).slice(0, limit);

    return NextResponse.json(perfMetrics);
  } catch (error) {
    console.error('Error in performance API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      metric_name,
      metric_type,
      endpoint,
      duration_ms,
      memory_usage_mb,
      cpu_usage_percent,
      user_id,
      session_id,
      metadata
    } = body;

    // Log performance metric
    const { data, error } = await supabase.rpc('error_reporting.log_performance_metric' as any, {
      p_metric_name: metric_name,
      p_metric_type: metric_type,
      p_endpoint: endpoint,
      p_duration_ms: duration_ms,
      p_memory_usage_mb: memory_usage_mb,
      p_cpu_usage_percent: cpu_usage_percent,
      p_user_id: user_id,
      p_session_id: session_id,
      p_metadata: metadata || {}
    });

    if (error) {
      console.error('Error logging performance metric:', error);
      return NextResponse.json(
        { error: 'Failed to log performance metric' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true, id: data });
  } catch (error) {
    console.error('Error in performance POST API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
