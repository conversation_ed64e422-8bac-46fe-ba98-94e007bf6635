'use client'

import type { ReactNode } from 'react'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { usePathname, useRouter } from 'next/navigation'

interface DeadlinesLayoutProps {
  children: ReactNode
}

export default function DeadlinesLayout({ children }: DeadlinesLayoutProps) {
  const pathname = usePathname()
  const router = useRouter()

  const isActive = (path: string) => {
    return pathname.includes(path)
  }

  return (
    <div className="container mx-auto py-6 max-w-7xl">
      <h1 className="text-3xl font-bold mb-6">Legal Deadlines</h1>

      <Tabs
        value={isActive('/validation') ? 'validation' : 'all'}
        className="mb-6"
        onValueChange={(value) => {
          if (value === 'validation') {
            router.push('/deadlines/validation')
          } else {
            router.push('/deadlines')
          }
        }}
      >
        <TabsList>
          <TabsTrigger value="all">All Deadlines</TabsTrigger>
          <TabsTrigger value="validation">Validation</TabsTrigger>
        </TabsList>
      </Tabs>

      {children}
    </div>
  )
}
