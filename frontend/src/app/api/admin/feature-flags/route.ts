/**
 * Feature Flags API Endpoint for Superadmin Dashboard
 * 
 * Provides current feature flag status and deployment phase information
 * for the MCP Rules Engine monitoring dashboard.
 */

import type { NextRequest} from 'next/server';
import { NextResponse } from 'next/server';
import { FEATURE_FLAGS, DEPLOYMENT_PHASE, IS_DEVELOPMENT, IS_STAGING, IS_PRODUCTION } from '@/lib/config';

export async function GET(request: NextRequest) {
  try {
    // Get tenant ID from query params for tenant-specific flags
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    // Pilot tenant configuration for D3 phase
    const pilotTenants = ['pilot-smith', 'test-tenant-1', 'dev-tenant'];
    const isPilotTenant = tenantId ? pilotTenants.includes(tenantId) : false;

    // Calculate effective MCP enablement based on deployment phase
    let mcpEnabled = FEATURE_FLAGS.MCP_RULES_ENGINE;
    
    // D2 Staging Mode: Only enabled in staging environment
    if (FEATURE_FLAGS.D2_STAGING_MODE) {
      mcpEnabled = IS_STAGING || IS_DEVELOPMENT;
    }
    
    // D3 Pilot Mode: Only enabled for specific tenants
    if (FEATURE_FLAGS.D3_PILOT_TENANT && tenantId) {
      mcpEnabled = isPilotTenant;
    }

    const featureFlags = {
      // Core MCP flags
      MCP_RULES_ENGINE: mcpEnabled,
      MCP_DEBUG_LOGGING: FEATURE_FLAGS.MCP_DEBUG_LOGGING || IS_DEVELOPMENT,
      MCP_METRICS_ENABLED: FEATURE_FLAGS.MCP_METRICS_ENABLED,
      
      // Deployment phase flags
      D2_STAGING_MODE: FEATURE_FLAGS.D2_STAGING_MODE,
      D3_PILOT_TENANT: FEATURE_FLAGS.D3_PILOT_TENANT,
      
      // Environment flags
      IS_DEVELOPMENT,
      IS_STAGING,
      IS_PRODUCTION,
    };

    const deploymentInfo = {
      phase: DEPLOYMENT_PHASE,
      environment: IS_DEVELOPMENT ? 'development' : IS_STAGING ? 'staging' : 'production',
      tenantId,
      isPilotTenant,
      effectiveMcpEnabled: mcpEnabled,
    };

    // Simulated metrics (in production, this would come from monitoring service)
    const metrics = {
      totalRequests: Math.floor(Math.random() * 2000) + 1000,
      successRate: 99.2 + Math.random() * 0.8,
      avgLatency: Math.floor(Math.random() * 200) + 300,
      errorRate: Math.random() * 1.5,
      lastUpdated: new Date().toISOString(),
      
      // D2 Performance targets
      targets: {
        errorRate: 1.0, // < 1%
        latency: 700,   // < 700ms
      }
    };

    const response = {
      success: true,
      _data: {
        featureFlags,
        deploymentInfo,
        metrics,
        timestamp: new Date().toISOString(),
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching feature flags:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch feature flags',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { flag, enabled, tenantId } = body;

    // In a real implementation, this would update the feature flag
    // For now, we'll just log the request
    console.log(`[Feature Flag Update] ${flag}: ${enabled}`, { tenantId });

    // Simulate feature flag update
    const response = {
      success: true,
      message: `Feature flag ${flag} ${enabled ? 'enabled' : 'disabled'}`,
      data: {
        flag,
        enabled,
        tenantId,
        updatedAt: new Date().toISOString(),
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error updating feature flag:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update feature flag',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
